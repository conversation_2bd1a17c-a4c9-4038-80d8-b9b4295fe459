[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[project]
name = "code_standard_mcp"
version = "1.0.17"
description = "CodeStandardMCP - 编码规范服务工具，提供规范查询、合规检查和管理功能的MCP服务器"
readme = "README.md"
requires-python = ">=3.10"
dependencies = [
    "httpx>=0.28.1",
    "fastmcp>=2.0.0",
    "pydantic>=2.0.0",
    "aiofiles>=23.0.0",
]

[project.scripts]
code-standard-mcp = "main:main"

[tool.hatch.build.targets.wheel]
packages = ["."]

# UV 包管理器配置 - 使用中国镜像源
[tool.uv]
index-url = "https://pypi.tuna.tsinghua.edu.cn/simple"
extra-index-url = [
    "https://mirrors.aliyun.com/pypi/simple/",
    "https://pypi.douban.com/simple/"
]
