{"metadata": {"selector": {"language": "python", "framework": "django", "version": null, "question": "模型字段命名规范"}, "fetch_date": "2025-07-18 11:08:31.570709", "source_url": "http://10.12.135.167:9090/api/local_doc_qa/local_doc_chat", "content_hash": "8d22da25be02173613089a15fd355e50"}, "content": "在Django框架中，模型字段命名应遵循以下规范：\n\n1. **使用下划线命名法**：字段名应使用小写字母，单词间用下划线分隔，如 `create_time`。\n2. **保持一致性**：尽量使用已存在的字段名，以保持数据库的一致性和可维护性。\n3. **字段类型**：根据字段的实际用途选择合适的字段类型，如 `IntegerField`、`CharField`、`BooleanField` 等。\n4. **注释**：为字段添加注释，说明字段的用途和限制，如 `help_text` 参数。\n5. **长度限制**：考虑到数据库的兼容性，字段名长度应控制在30个字符以内。\n\n示例：\n```python\nfrom django.db import models\n\nclass ExampleModel(models.Model):\n    create_time = models.DateTimeField(auto_now_add=True, help_text=\"创建时间\")\n    user_name = models.CharField(max_length=100, help_text=\"用户名\")\n    is_active = models.BooleanField(default=True, help_text=\"是否激活\")\n```"}