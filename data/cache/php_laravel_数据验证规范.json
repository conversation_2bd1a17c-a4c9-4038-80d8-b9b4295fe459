{"metadata": {"selector": {"language": "php", "framework": "laravel", "version": null, "question": "数据验证规范"}, "fetch_date": "2025-07-18 10:48:39.558770", "source_url": "http://10.12.135.167:9090/api/local_doc_qa/local_doc_chat", "content_hash": "6a03945cf29844cc00fede7775fc45b5"}, "content": "在Laravel框架中，数据验证是一个非常重要的部分，用于确保用户提交的数据符合预期的格式和规则。Laravel提供了一套强大且灵活的验证机制，可以通过多种方式实现数据验证，包括使用控制器中的验证方法、表单请求类、以及自定义验证规则。\n\n### 验证方式\n\n1. **控制器中的验证**：\n   - 在控制器方法中直接使用`validate`方法进行数据验证。\n   - 例如：\n     ```php\n     public function store(Request $request)\n     {\n         $validated = $request->validate([\n             'title' => 'required|max:255',\n             'body' => 'required',\n         ]);\n\n         // 使用验证通过的数据\n     }\n     ```\n\n2. **表单请求类**：\n   - 创建一个表单请求类，将验证逻辑从控制器中分离出来。\n   - 例如：\n     ```php\n     php artisan make:request StorePostRequest\n     ```\n   - 在生成的请求类中定义验证规则：\n     ```php\n     public function rules()\n     {\n         return [\n             'title' => 'required|max:255',\n             'body' => 'required',\n         ];\n     }\n     ```\n   - 在控制器中使用该请求类：\n     ```php\n     public function store(StorePostRequest $request)\n     {\n         // 验证通过的数据\n     }\n     ```\n\n3. **自定义验证规则**：\n   - 可以通过`Validator::extend`方法自定义验证规则。\n   - 例如：\n     ```php\n     Validator::extend('foo', function ($attribute, $value, $parameters, $validator) {\n         return $value == 'foo';\n     });\n     ```\n\n### 验证规则\n\nLaravel提供了丰富的内置验证规则，如`required`、`email`、`min`、`max`等。这些规则可以组合使用，以满足复杂的验证需求。\n\n### 错误处理\n\n- 验证失败时，Laravel会自动将用户重定向回上一个页面，并将错误信息存储在会话中。\n- 可以在视图中使用`$errors`变量来显示验证错误信息：\n  ```php\n  @if ($errors->any())\n      <div class=\"alert alert-danger\">\n          <ul>\n              @foreach ($errors->all() as $error)\n                  <li>{{ $error }}</li>\n              @endforeach\n          </ul>\n      </div>\n  @endif\n  ```\n\n### 最佳实践\n\n- **保持控制器简洁**：将复杂的验证逻辑放在表单请求类中。\n- **使用自定义错误消息**：可以通过`messages`方法自定义验证错误消息。\n- **国际化**：确保验证错误消息支持多语言，使用Laravel的本地化功能。\n\n通过以上规范，可以确保Laravel应用中的数据验证既强大又灵活，同时保持代码的清晰和可维护性。"}