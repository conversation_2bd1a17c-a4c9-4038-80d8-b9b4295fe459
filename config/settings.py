"""
CodeStandardMCP 配置管理模块

提供简化的服务器配置功能
"""

import os
from pathlib import Path
from typing import Optional


class ServerConfig:
    """简化的服务器配置管理类"""

    def __init__(self):
        # 基础配置
        self.name = "CodeStandardMCP"
        self.version = "1.0.0"
        self.description = "编码规范服务工具"

        # 网络配置 - 延长超时时间为2分钟
        self.http_timeout = float(os.getenv("CSMCP_HTTP_TIMEOUT", "120.0"))  # 总超时120秒
        self.connect_timeout = float(os.getenv("CSMCP_CONNECT_TIMEOUT", "60.0"))  # 连接超时60秒
        self.read_timeout = float(os.getenv("CSMCP_READ_TIMEOUT", "120.0"))  # 读取超时120秒
        self.write_timeout = float(os.getenv("CSMCP_WRITE_TIMEOUT", "10.0"))  # 写入超时10秒
        self.pool_timeout = float(os.getenv("CSMCP_POOL_TIMEOUT", "4.0"))  # 连接池超时4秒
        self.user_agent = os.getenv("CSMCP_USER_AGENT", "CodeStandardMCP/1.0 (httpx)")
        self.max_retries = int(os.getenv("CSMCP_MAX_RETRIES", "1"))  # 最多重试1次，避免MCP超时

        # 缓存配置
        self.cache_ttl_hours = int(os.getenv("CSMCP_CACHE_TTL", "24"))

        # 流式处理配置
        self.max_content_size = int(os.getenv("CSMCP_MAX_CONTENT_SIZE", "10485760"))  # 10MB

        # 日志配置
        self.log_level = os.getenv("CSMCP_LOG_LEVEL", "INFO")
        self.log_file = os.getenv("CSMCP_LOG_FILE", None)
        self.mask_error_details = (
            os.getenv("CSMCP_MASK_ERROR_DETAILS", "false").lower() == "true"
        )

        # API调用日志配置
        self.log_api_details = (
            os.getenv("CSMCP_LOG_API_DETAILS", "true").lower() == "true"
        )
        self.mask_sensitive_data = (
            os.getenv("CSMCP_MASK_SENSITIVE_DATA", "true").lower() == "true"
        )

        # 存储配置
        self.data_dir = Path(os.getenv("CSMCP_DATA_DIR", "./data"))
        self.cache_dir = self.data_dir / "cache"

        # 反馈数据库配置
        self.local_feedback_db_path = self.data_dir / "feedback.db"
        self.remote_feedback_db_path = self._get_remote_db_path()

        # 反馈同步配置
        self.feedback_sync_enabled = os.getenv("CSMCP_FEEDBACK_SYNC_ENABLED", "true").lower() == "true"
        self.feedback_sync_retry_count = int(os.getenv("CSMCP_FEEDBACK_SYNC_RETRY_COUNT", "3"))
        self.feedback_sync_timeout = int(os.getenv("CSMCP_FEEDBACK_SYNC_TIMEOUT", "30"))

        # 确保目录存在
        self._ensure_directories()

        # 编码规范API配置 - 可通过环境变量覆盖
        self.api_url = os.getenv("CSMCP_API_URL", "http://10.12.135.167:9090/api/local_doc_qa/local_doc_chat")
        self.api_user_id = int(os.getenv("CSMCP_API_USER_ID", "55"))
        self.response_limit_chars = int(os.getenv("CSMCP_API_RESPONSE_LIMIT", "200"))


    def _get_remote_db_path(self) -> Optional[Path]:
        """获取远程数据库路径"""
        remote_path = os.getenv("CSMCP_REMOTE_FEEDBACK_DB_PATH")
        if remote_path:
            return Path(remote_path)
        return None

    def _ensure_directories(self):
        """确保必要的目录存在"""
        for directory in [self.data_dir, self.cache_dir]:
            directory.mkdir(parents=True, exist_ok=True)

    def get_cache_file_path(self, cache_key: str) -> Path:
        """获取缓存文件路径"""
        return self.cache_dir / f"{cache_key}.json"

    def get_api_url(self) -> str:
        """获取编码规范API接口地址"""
        return self.api_url

    def get_api_user_id(self) -> int:
        """获取API调用用户ID"""
        return self.api_user_id

    def get_response_limit(self) -> int:
        """获取API响应字符限制"""
        return self.response_limit_chars

    def should_log_api_details(self) -> bool:
        """是否记录API调用详细信息"""
        return self.log_api_details

    def should_mask_sensitive_data(self) -> bool:
        """是否掩码敏感数据"""
        return self.mask_sensitive_data

    def mask_user_id(self, user_id: int) -> str:
        """掩码用户ID"""
        if not self.should_mask_sensitive_data():
            return str(user_id)
        user_str = str(user_id)
        if len(user_str) <= 2:
            return "***"
        return "***" + user_str[-2:]

    def truncate_text(self, text: str, max_length: int = 50) -> str:
        """截断文本用于日志显示"""
        if not text:
            return ""
        if len(text) <= max_length:
            return text
        return text[:max_length] + "..."


# 全局配置实例
config = ServerConfig()
