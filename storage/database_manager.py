"""
数据库管理器

提供SQLite数据库连接和操作功能，支持本地和远程数据库
"""

import asyncio
import logging
from datetime import datetime
from pathlib import Path
from typing import Any, Dict, List, Optional

import aiosqlite

from config.settings import config

logger = logging.getLogger("CodeStandardMCP.DatabaseManager")


class DatabaseManager:
    """数据库管理器类"""

    def __init__(self):
        self.local_db_path = config.local_feedback_db_path
        self.remote_db_path = config.remote_feedback_db_path
        self._local_connection = None
        self._remote_connection = None

    async def initialize_databases(self):
        """初始化数据库，创建表结构"""
        try:
            # 初始化本地数据库
            await self._initialize_local_db()
            
            # 尝试初始化远程数据库
            await self._initialize_remote_db()
            
            logger.info("数据库初始化完成")
            return True
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            return False

    async def _initialize_local_db(self):
        """初始化本地数据库"""
        # 确保本地数据库目录存在
        self.local_db_path.parent.mkdir(parents=True, exist_ok=True)
        
        async with aiosqlite.connect(self.local_db_path) as db:
            await db.execute(self._get_create_table_sql())
            await db.commit()
            logger.info(f"本地数据库初始化完成: {self.local_db_path}")

    async def _initialize_remote_db(self):
        """初始化远程数据库"""
        if not self.remote_db_path:
            logger.info("未配置远程数据库路径，跳过远程数据库初始化")
            return

        try:
            # 确保远程数据库目录存在（如果是本地网络路径）
            if self.remote_db_path.is_absolute():
                self.remote_db_path.parent.mkdir(parents=True, exist_ok=True)
            
            async with aiosqlite.connect(self.remote_db_path) as db:
                await db.execute(self._get_create_table_sql())
                await db.commit()
                logger.info(f"远程数据库初始化完成: {self.remote_db_path}")
                
        except Exception as e:
            logger.warning(f"远程数据库初始化失败: {e}")

    def _get_create_table_sql(self) -> str:
        """获取创建表的SQL语句"""
        return """
        CREATE TABLE IF NOT EXISTS feedback (
            id INTEGER PRIMARY KEY AUTOINCREMENT,
            question TEXT NOT NULL,
            answer TEXT NOT NULL,
            rating INTEGER CHECK(rating >= 1 AND rating <= 5) NOT NULL,
            comment TEXT,
            session_id TEXT,
            user_id TEXT,
            created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
            synced_at TIMESTAMP,
            sync_status TEXT DEFAULT 'pending'
        );
        
        CREATE INDEX IF NOT EXISTS idx_feedback_sync_status ON feedback(sync_status);
        CREATE INDEX IF NOT EXISTS idx_feedback_created_at ON feedback(created_at);
        CREATE INDEX IF NOT EXISTS idx_feedback_session_id ON feedback(session_id);
        """

    async def get_local_connection(self) -> aiosqlite.Connection:
        """获取本地数据库连接"""
        return await aiosqlite.connect(self.local_db_path)

    async def get_remote_connection(self) -> Optional[aiosqlite.Connection]:
        """获取远程数据库连接"""
        if not self.remote_db_path:
            return None
            
        try:
            return await aiosqlite.connect(self.remote_db_path)
        except Exception as e:
            logger.error(f"连接远程数据库失败: {e}")
            return None

    async def execute_query(
        self, 
        sql: str, 
        params: tuple = (), 
        use_remote: bool = False
    ) -> List[Dict[str, Any]]:
        """执行查询语句"""
        try:
            if use_remote:
                conn = await self.get_remote_connection()
                if not conn:
                    raise Exception("无法连接到远程数据库")
            else:
                conn = await self.get_local_connection()
            
            async with conn:
                conn.row_factory = aiosqlite.Row
                async with conn.execute(sql, params) as cursor:
                    rows = await cursor.fetchall()
                    return [dict(row) for row in rows]
                    
        except Exception as e:
            logger.error(f"执行查询失败: {e}")
            raise

    async def execute_insert(
        self, 
        sql: str, 
        params: tuple = (), 
        use_remote: bool = False
    ) -> int:
        """执行插入语句，返回插入的行ID"""
        try:
            if use_remote:
                conn = await self.get_remote_connection()
                if not conn:
                    raise Exception("无法连接到远程数据库")
            else:
                conn = await self.get_local_connection()
            
            async with conn:
                cursor = await conn.execute(sql, params)
                await conn.commit()
                return cursor.lastrowid
                
        except Exception as e:
            logger.error(f"执行插入失败: {e}")
            raise

    async def execute_update(
        self, 
        sql: str, 
        params: tuple = (), 
        use_remote: bool = False
    ) -> int:
        """执行更新语句，返回影响的行数"""
        try:
            if use_remote:
                conn = await self.get_remote_connection()
                if not conn:
                    raise Exception("无法连接到远程数据库")
            else:
                conn = await self.get_local_connection()
            
            async with conn:
                cursor = await conn.execute(sql, params)
                await conn.commit()
                return cursor.rowcount
                
        except Exception as e:
            logger.error(f"执行更新失败: {e}")
            raise

    async def test_connections(self) -> Dict[str, bool]:
        """测试数据库连接"""
        result = {"local": False, "remote": False}
        
        # 测试本地连接
        try:
            async with await self.get_local_connection() as conn:
                await conn.execute("SELECT 1")
                result["local"] = True
                logger.info("本地数据库连接正常")
        except Exception as e:
            logger.error(f"本地数据库连接失败: {e}")
        
        # 测试远程连接
        try:
            remote_conn = await self.get_remote_connection()
            if remote_conn:
                async with remote_conn:
                    await remote_conn.execute("SELECT 1")
                    result["remote"] = True
                    logger.info("远程数据库连接正常")
        except Exception as e:
            logger.error(f"远程数据库连接失败: {e}")
        
        return result


# 全局数据库管理器实例
database_manager = DatabaseManager()
