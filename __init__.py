"""CodeStandardMCP - 编码规范即服务"""

__version__ = "1.0.0"
__author__ = "CodeStandardMCP"
__description__ = "编码规范服务工具，提供规范查询、合规检查和管理功能的MCP服务器"

from .config.settings import config
from .models.standard import StandardSelector, StandardDocument, StandardMetadata
from .models.feedback import FeedbackSubmission, FeedbackData, FeedbackStats, SyncResult
from .models.qa_session import QASessionData, QASessionRequest
from .services.standard_fetcher import standard_fetcher
from .services.standards_manager import standards_manager
from .services.feedback_service import feedback_service
from .services.qa_session_service import qa_session_service
from .storage.database_manager import database_manager

__all__ = [
    "config",
    "StandardSelector",
    "StandardDocument",
    "StandardMetadata",
    "FeedbackSubmission",
    "FeedbackData",
    "FeedbackStats",
    "SyncResult",
    "QASessionData",
    "QASessionRequest",
    "standard_fetcher",
    "standards_manager",
    "feedback_service",
    "qa_session_service",
    "database_manager",
]
