# 远程反馈服务 - 极简实体定义

## 1. 用户反馈 (Feedback) - 核心实体

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| feedback_id | UUID | 是 | 反馈唯一标识 |
| session_id | UUID | 是 | 会话标识（来自客户端） |
| question | Text | 是 | 用户问题 |
| answer | Text | 是 | AI回答 |
| rating | Integer | 是 | 用户评分(1-5) |
| comment | Text | 否 | 用户评价文字 |
| language | String(50) | 否 | 编程语言 |
| framework | String(50) | 否 | 框架名称 |
| created_at | DateTime | 是 | 创建时间 |

## 2. 租户 (Tenant) - 可选实体

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tenant_id | UUID | 是 | 租户唯一标识 |
| api_key | String(255) | 是 | API访问密钥 |
| name | String(255) | 是 | 租户名称 |
| created_at | DateTime | 是 | 创建时间 |

## 3. 核心约束

- `rating` 必须在 1-5 之间
- `session_id` 在反馈表中唯一（一个会话只能有一个反馈）
- `api_key` 在租户表中唯一

## 4. 索引建议

### 反馈表
- `session_id` (唯一索引)
- `created_at`
- `rating`
- `language`

### 租户表
- `api_key` (唯一索引)

## 5. 数据库表结构

```sql
-- 租户表（如果需要多租户支持）
CREATE TABLE tenants (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    api_key VARCHAR(255) UNIQUE NOT NULL,
    name VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 反馈表（核心表）
CREATE TABLE feedback (
    feedback_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID UNIQUE NOT NULL,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    rating INTEGER CHECK(rating >= 1 AND rating <= 5) NOT NULL,
    comment TEXT,
    language VARCHAR(50),
    framework VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 索引
CREATE UNIQUE INDEX idx_feedback_session_id ON feedback(session_id);
CREATE INDEX idx_feedback_created_at ON feedback(created_at);
CREATE INDEX idx_feedback_rating ON feedback(rating);
CREATE INDEX idx_feedback_language ON feedback(language);
```

## 6. API接口（极简版）

### 6.1 提交反馈
```
POST /api/v1/feedback
{
  "session_id": "550e8400-e29b-41d4-a716-************",
  "question": "Python函数命名规范",
  "answer": "使用小写字母和下划线...",
  "rating": 5,
  "comment": "很有帮助",
  "language": "python",
  "framework": "django"
}
```

### 6.2 获取反馈
```
GET /api/v1/feedback/{session_id}
```

### 6.3 查询反馈列表
```
GET /api/v1/feedback?page=1&limit=20&rating=5&language=python
```

### 6.4 健康检查
```
GET /api/v1/health
```

## 7. 统计查询（通过SQL实现）

```sql
-- 平均评分
SELECT AVG(rating) as average_rating FROM feedback;

-- 评分分布
SELECT rating, COUNT(*) as count 
FROM feedback 
GROUP BY rating 
ORDER BY rating;

-- 语言分布
SELECT language, COUNT(*) as count 
FROM feedback 
WHERE language IS NOT NULL 
GROUP BY language 
ORDER BY count DESC;

-- 每日反馈数
SELECT DATE(created_at) as date, COUNT(*) as count 
FROM feedback 
GROUP BY DATE(created_at) 
ORDER BY date;
```

这个极简版本将问答会话和反馈合并为一个实体，大大简化了数据结构，同时保留了核心功能。
