# 远程反馈服务设计规范

## 1. 服务概述

### 1.1 服务目标
远程反馈服务是一个独立的HTTP API服务，用于集中管理来自多个CodeStandardMCP客户端的问答会话和用户反馈数据。

### 1.2 核心功能
- 问答会话管理
- 用户反馈收集和存储
- 数据统计和分析
- 多租户支持
- 数据同步和备份

### 1.3 技术栈建议
- **后端框架**: FastAPI (Python) / Express.js (Node.js) / Spring Boot (Java)
- **数据库**: PostgreSQL (推荐) / MySQL / SQLite
- **缓存**: Redis (可选)
- **部署**: Docker + Docker Compose
- **监控**: Prometheus + Grafana (可选)

## 2. 数据结构设计

### 2.1 核心实体

#### 2.1.1 租户 (Tenant)
```json
{
  "tenant_id": "string",           // 租户ID (UUID)
  "name": "string",                // 租户名称
  "api_key": "string",             // API密钥
  "created_at": "datetime",        // 创建时间
  "updated_at": "datetime",        // 更新时间
  "is_active": "boolean",          // 是否激活
  "settings": {                    // 租户配置
    "max_sessions_per_day": "integer",
    "max_feedback_per_day": "integer",
    "data_retention_days": "integer"
  }
}
```

#### 2.1.2 问答会话 (QASession)
```json
{
  "session_id": "string",          // 会话ID (UUID)
  "tenant_id": "string",           // 租户ID
  "question": "string",            // 用户问题
  "answer": "string",              // AI回答
  "language": "string",            // 编程语言
  "framework": "string",           // 框架名称 (可选)
  "version": "string",             // 版本信息 (可选)
  "question_detail": "string",     // 具体问题内容 (可选)
  "source": "string",              // 回答来源 (remote/cache)
  "content_hash": "string",        // 内容哈希
  "metadata": {                    // 扩展元数据
    "client_version": "string",
    "request_id": "string",
    "response_time_ms": "integer",
    "cache_hit": "boolean"
  },
  "created_at": "datetime",        // 创建时间
  "updated_at": "datetime"         // 更新时间
}
```

#### 2.1.3 用户反馈 (Feedback)
```json
{
  "feedback_id": "string",         // 反馈ID (UUID)
  "session_id": "string",          // 关联的会话ID
  "tenant_id": "string",           // 租户ID
  "rating": "integer",             // 评分 (1-5)
  "comment": "string",             // 评价文字 (可选)
  "user_id": "string",             // 用户ID (可选)
  "user_agent": "string",          // 用户代理 (可选)
  "ip_address": "string",          // IP地址 (可选，脱敏)
  "tags": ["string"],              // 标签 (可选)
  "metadata": {                    // 扩展元数据
    "client_version": "string",
    "submission_source": "string"
  },
  "created_at": "datetime",        // 创建时间
  "updated_at": "datetime"         // 更新时间
}
```

#### 2.1.4 统计数据 (Statistics)
```json
{
  "stat_id": "string",             // 统计ID
  "tenant_id": "string",           // 租户ID
  "stat_type": "string",           // 统计类型 (daily/weekly/monthly)
  "date": "date",                  // 统计日期
  "data": {                        // 统计数据
    "total_sessions": "integer",
    "total_feedback": "integer",
    "average_rating": "float",
    "rating_distribution": {
      "1": "integer",
      "2": "integer",
      "3": "integer",
      "4": "integer",
      "5": "integer"
    },
    "language_distribution": {
      "python": "integer",
      "javascript": "integer",
      "java": "integer"
    },
    "framework_distribution": {
      "django": "integer",
      "react": "integer",
      "spring": "integer"
    }
  },
  "created_at": "datetime"
}
```

### 2.2 数据库表结构

#### 2.2.1 租户表 (tenants)
```sql
CREATE TABLE tenants (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    settings JSONB DEFAULT '{}',
    is_active BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_tenants_api_key ON tenants(api_key);
CREATE INDEX idx_tenants_is_active ON tenants(is_active);
```

#### 2.2.2 问答会话表 (qa_sessions)
```sql
CREATE TABLE qa_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    language VARCHAR(50),
    framework VARCHAR(50),
    version VARCHAR(50),
    question_detail TEXT,
    source VARCHAR(20) DEFAULT 'remote',
    content_hash VARCHAR(64),
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_qa_sessions_tenant_id ON qa_sessions(tenant_id);
CREATE INDEX idx_qa_sessions_created_at ON qa_sessions(created_at);
CREATE INDEX idx_qa_sessions_language ON qa_sessions(language);
CREATE INDEX idx_qa_sessions_content_hash ON qa_sessions(content_hash);
CREATE INDEX idx_qa_sessions_source ON qa_sessions(source);
```

#### 2.2.3 反馈表 (feedback)
```sql
CREATE TABLE feedback (
    feedback_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES qa_sessions(session_id),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    rating INTEGER CHECK(rating >= 1 AND rating <= 5) NOT NULL,
    comment TEXT,
    user_id VARCHAR(255),
    user_agent TEXT,
    ip_address INET,
    tags TEXT[],
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE INDEX idx_feedback_tenant_id ON feedback(tenant_id);
CREATE INDEX idx_feedback_session_id ON feedback(session_id);
CREATE INDEX idx_feedback_rating ON feedback(rating);
CREATE INDEX idx_feedback_created_at ON feedback(created_at);
CREATE INDEX idx_feedback_user_id ON feedback(user_id);
```

#### 2.2.4 统计表 (statistics)
```sql
CREATE TABLE statistics (
    stat_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    stat_type VARCHAR(20) NOT NULL,
    date DATE NOT NULL,
    data JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    
    UNIQUE(tenant_id, stat_type, date)
);

CREATE INDEX idx_statistics_tenant_id ON statistics(tenant_id);
CREATE INDEX idx_statistics_date ON statistics(date);
CREATE INDEX idx_statistics_type ON statistics(stat_type);
```

## 3. API接口设计

### 3.1 认证方式
所有API请求需要在Header中包含API密钥：
```
Authorization: Bearer <api_key>
```

### 3.2 通用响应格式

#### 成功响应
```json
{
  "success": true,
  "data": {},
  "message": "操作成功",
  "timestamp": "2024-01-01T00:00:00Z"
}
```

#### 错误响应
```json
{
  "success": false,
  "error": {
    "code": "VALIDATION_ERROR",
    "message": "请求参数验证失败",
    "details": {}
  },
  "timestamp": "2024-01-01T00:00:00Z"
}
```

### 3.3 问答会话管理接口

#### 3.3.1 创建问答会话
```
POST /api/v1/qa-sessions
Content-Type: application/json

Request Body:
{
  "question": "Python函数命名规范的具体要求",
  "answer": "Python函数命名应该使用小写字母...",
  "language": "python",
  "framework": "django",
  "version": "4.2",
  "question_detail": "函数命名规范",
  "source": "remote",
  "metadata": {
    "client_version": "1.0.0",
    "request_id": "req_123",
    "response_time_ms": 1500,
    "cache_hit": false
  }
}

Response:
{
  "success": true,
  "data": {
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "content_hash": "abc123def456",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 3.3.2 获取问答会话详情
```
GET /api/v1/qa-sessions/{session_id}

Response:
{
  "success": true,
  "data": {
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "question": "Python函数命名规范的具体要求",
    "answer": "Python函数命名应该使用小写字母...",
    "language": "python",
    "framework": "django",
    "version": "4.2",
    "question_detail": "函数命名规范",
    "source": "remote",
    "content_hash": "abc123def456",
    "metadata": {},
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 3.3.3 查询问答会话列表
```
GET /api/v1/qa-sessions?page=1&limit=20&language=python&framework=django&start_date=2024-01-01&end_date=2024-01-31

Response:
{
  "success": true,
  "data": {
    "sessions": [
      {
        "session_id": "550e8400-e29b-41d4-a716-446655440000",
        "question": "Python函数命名规范的具体要求",
        "language": "python",
        "framework": "django",
        "source": "remote",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 100,
      "pages": 5
    }
  }
}
```

### 3.4 反馈管理接口

#### 3.4.1 提交反馈
```
POST /api/v1/feedback
Content-Type: application/json

Request Body:
{
  "session_id": "550e8400-e29b-41d4-a716-446655440000",
  "rating": 5,
  "comment": "回答很详细，很有帮助！",
  "user_id": "user_123",
  "tags": ["helpful", "detailed"],
  "metadata": {
    "client_version": "1.0.0",
    "submission_source": "web"
  }
}

Response:
{
  "success": true,
  "data": {
    "feedback_id": "660e8400-e29b-41d4-a716-446655440000",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 3.4.2 获取反馈详情
```
GET /api/v1/feedback/{feedback_id}

Response:
{
  "success": true,
  "data": {
    "feedback_id": "660e8400-e29b-41d4-a716-446655440000",
    "session_id": "550e8400-e29b-41d4-a716-446655440000",
    "rating": 5,
    "comment": "回答很详细，很有帮助！",
    "user_id": "user_123",
    "tags": ["helpful", "detailed"],
    "metadata": {},
    "created_at": "2024-01-01T00:00:00Z",
    "updated_at": "2024-01-01T00:00:00Z"
  }
}
```

#### 3.4.3 查询反馈列表
```
GET /api/v1/feedback?page=1&limit=20&rating=5&start_date=2024-01-01&end_date=2024-01-31

Response:
{
  "success": true,
  "data": {
    "feedback": [
      {
        "feedback_id": "660e8400-e29b-41d4-a716-446655440000",
        "session_id": "550e8400-e29b-41d4-a716-446655440000",
        "rating": 5,
        "comment": "回答很详细，很有帮助！",
        "user_id": "user_123",
        "created_at": "2024-01-01T00:00:00Z"
      }
    ],
    "pagination": {
      "page": 1,
      "limit": 20,
      "total": 50,
      "pages": 3
    }
  }
}
```

### 3.5 统计信息接口

#### 3.5.1 获取反馈统计
```
GET /api/v1/statistics/feedback?period=daily&start_date=2024-01-01&end_date=2024-01-31

Response:
{
  "success": true,
  "data": {
    "summary": {
      "total_feedback": 1250,
      "average_rating": 4.2,
      "rating_distribution": {
        "1": 50,
        "2": 100,
        "3": 200,
        "4": 400,
        "5": 500
      }
    },
    "trends": [
      {
        "date": "2024-01-01",
        "total_feedback": 45,
        "average_rating": 4.1
      },
      {
        "date": "2024-01-02",
        "total_feedback": 52,
        "average_rating": 4.3
      }
    ]
  }
}
```

#### 3.5.2 获取问答会话统计
```
GET /api/v1/statistics/qa-sessions?period=weekly&start_date=2024-01-01&end_date=2024-01-31

Response:
{
  "success": true,
  "data": {
    "summary": {
      "total_sessions": 2500,
      "unique_languages": 8,
      "unique_frameworks": 15,
      "language_distribution": {
        "python": 800,
        "javascript": 600,
        "java": 400,
        "go": 300,
        "rust": 200,
        "others": 200
      },
      "framework_distribution": {
        "django": 300,
        "react": 250,
        "spring": 200,
        "express": 180,
        "others": 570
      }
    },
    "trends": [
      {
        "week": "2024-W01",
        "total_sessions": 180,
        "cache_hit_rate": 0.65
      }
    ]
  }
}
```

#### 3.5.3 获取仪表板数据
```
GET /api/v1/statistics/dashboard

Response:
{
  "success": true,
  "data": {
    "overview": {
      "total_sessions_today": 125,
      "total_feedback_today": 89,
      "average_rating_today": 4.3,
      "active_users_today": 67
    },
    "recent_activity": [
      {
        "type": "feedback",
        "rating": 5,
        "comment": "很有帮助",
        "language": "python",
        "created_at": "2024-01-01T12:30:00Z"
      }
    ],
    "top_languages": [
      {"language": "python", "count": 45},
      {"language": "javascript", "count": 38}
    ],
    "satisfaction_trend": [
      {"date": "2024-01-01", "rating": 4.2},
      {"date": "2024-01-02", "rating": 4.3}
    ]
  }
}
```

### 3.6 系统管理接口

#### 3.6.1 健康检查
```
GET /api/v1/health

Response:
{
  "success": true,
  "data": {
    "status": "healthy",
    "version": "1.0.0",
    "uptime": "72h30m15s",
    "database": {
      "status": "connected",
      "response_time_ms": 5
    },
    "cache": {
      "status": "connected",
      "response_time_ms": 2
    },
    "metrics": {
      "requests_per_minute": 150,
      "error_rate": 0.02,
      "memory_usage_mb": 256
    }
  }
}
```

#### 3.6.2 批量同步
```
POST /api/v1/sync/batch
Content-Type: application/json

Request Body:
{
  "qa_sessions": [
    {
      "session_id": "550e8400-e29b-41d4-a716-446655440000",
      "question": "Python函数命名规范",
      "answer": "使用小写字母...",
      "language": "python"
    }
  ],
  "feedback": [
    {
      "session_id": "550e8400-e29b-41d4-a716-446655440000",
      "rating": 5,
      "comment": "很有帮助"
    }
  ]
}

Response:
{
  "success": true,
  "data": {
    "qa_sessions": {
      "created": 1,
      "updated": 0,
      "failed": 0
    },
    "feedback": {
      "created": 1,
      "updated": 0,
      "failed": 0
    },
    "errors": []
  }
}
```

## 4. 错误代码定义

### 4.1 通用错误代码
- `INVALID_API_KEY` - API密钥无效
- `RATE_LIMIT_EXCEEDED` - 请求频率超限
- `VALIDATION_ERROR` - 请求参数验证失败
- `INTERNAL_SERVER_ERROR` - 服务器内部错误
- `SERVICE_UNAVAILABLE` - 服务不可用

### 4.2 业务错误代码
- `SESSION_NOT_FOUND` - 问答会话不存在
- `FEEDBACK_NOT_FOUND` - 反馈不存在
- `DUPLICATE_FEEDBACK` - 重复反馈（同一会话已有反馈）
- `INVALID_RATING` - 评分值无效
- `CONTENT_TOO_LARGE` - 内容过大
- `TENANT_QUOTA_EXCEEDED` - 租户配额超限

## 5. 部署配置

### 5.1 环境变量配置
```bash
# 服务配置
PORT=8080
HOST=0.0.0.0
ENV=production

# 数据库配置
DATABASE_URL=postgresql://user:password@localhost:5432/feedback_service
DATABASE_POOL_SIZE=20
DATABASE_TIMEOUT=30

# Redis配置 (可选)
REDIS_URL=redis://localhost:6379/0
REDIS_TIMEOUT=5

# 安全配置
JWT_SECRET=your-secret-key
API_KEY_SALT=your-salt
CORS_ORIGINS=https://your-domain.com

# 限流配置
RATE_LIMIT_REQUESTS=1000
RATE_LIMIT_WINDOW=3600

# 日志配置
LOG_LEVEL=info
LOG_FORMAT=json
```

### 5.2 Docker Compose 示例
```yaml
version: '3.8'

services:
  feedback-service:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**************************************/feedback_service
      - REDIS_URL=redis://redis:6379/0
    depends_on:
      - db
      - redis
    restart: unless-stopped

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=feedback_service
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    restart: unless-stopped

  redis:
    image: redis:7-alpine
    restart: unless-stopped

  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - feedback-service
    restart: unless-stopped

volumes:
  postgres_data:
```

### 5.3 Nginx 配置示例
```nginx
upstream feedback_service {
    server feedback-service:8080;
}

server {
    listen 80;
    server_name api.feedback.example.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name api.feedback.example.com;

    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;

    location /api/ {
        proxy_pass http://feedback_service;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;

        # 限流
        limit_req zone=api burst=20 nodelay;

        # 超时设置
        proxy_connect_timeout 5s;
        proxy_send_timeout 10s;
        proxy_read_timeout 30s;
    }
}
```

## 6. 客户端集成

### 6.1 CodeStandardMCP 集成示例
```python
import httpx
from typing import Optional, Dict, Any

class FeedbackServiceClient:
    def __init__(self, base_url: str, api_key: str):
        self.base_url = base_url.rstrip('/')
        self.api_key = api_key
        self.client = httpx.AsyncClient(
            headers={"Authorization": f"Bearer {api_key}"},
            timeout=30.0
        )

    async def create_qa_session(self, session_data: Dict[str, Any]) -> Optional[str]:
        """创建问答会话，返回session_id"""
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/qa-sessions",
                json=session_data
            )
            response.raise_for_status()
            result = response.json()
            return result["data"]["session_id"]
        except Exception as e:
            logger.error(f"创建问答会话失败: {e}")
            return None

    async def submit_feedback(self, feedback_data: Dict[str, Any]) -> bool:
        """提交反馈"""
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/feedback",
                json=feedback_data
            )
            response.raise_for_status()
            return True
        except Exception as e:
            logger.error(f"提交反馈失败: {e}")
            return False

    async def batch_sync(self, qa_sessions: list, feedback: list) -> Dict[str, Any]:
        """批量同步数据"""
        try:
            response = await self.client.post(
                f"{self.base_url}/api/v1/sync/batch",
                json={"qa_sessions": qa_sessions, "feedback": feedback}
            )
            response.raise_for_status()
            return response.json()["data"]
        except Exception as e:
            logger.error(f"批量同步失败: {e}")
            return {"qa_sessions": {"failed": len(qa_sessions)}, "feedback": {"failed": len(feedback)}}
```

## 7. 监控和运维

### 7.1 关键指标
- **性能指标**: 响应时间、吞吐量、错误率
- **业务指标**: 会话创建数、反馈提交数、用户满意度
- **系统指标**: CPU使用率、内存使用率、数据库连接数
- **安全指标**: 异常请求数、API密钥使用情况

### 7.2 告警规则
- 响应时间超过1秒
- 错误率超过5%
- 数据库连接数超过80%
- 磁盘使用率超过85%
- 内存使用率超过90%

### 7.3 备份策略
- 数据库每日全量备份
- 数据库每小时增量备份
- 备份文件保留30天
- 定期恢复测试

这个设计提供了一个完整、可扩展的远程反馈服务架构，支持多租户、高并发和数据分析需求。
