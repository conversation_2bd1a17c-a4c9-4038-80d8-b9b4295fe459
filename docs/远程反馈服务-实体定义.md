# 远程反馈服务 - 实体定义

## 1. 租户 (Tenant)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| tenant_id | UUID | 是 | 租户唯一标识 |
| name | String(255) | 是 | 租户名称 |
| api_key | String(255) | 是 | API访问密钥 |
| is_active | Boolean | 是 | 是否激活，默认true |
| created_at | DateTime | 是 | 创建时间 |
| updated_at | DateTime | 是 | 更新时间 |

## 2. 问答会话 (QASession)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| session_id | UUID | 是 | 会话唯一标识 |
| tenant_id | UUID | 是 | 所属租户ID |
| question | Text | 是 | 用户问题 |
| answer | Text | 是 | AI回答 |
| language | String(50) | 否 | 编程语言 |
| framework | String(50) | 否 | 框架名称 |
| version | String(50) | 否 | 版本信息 |
| question_detail | Text | 否 | 具体问题内容 |
| source | String(20) | 是 | 回答来源，默认"remote" |
| content_hash | String(64) | 否 | 内容哈希值 |
| created_at | DateTime | 是 | 创建时间 |
| updated_at | DateTime | 是 | 更新时间 |

## 3. 用户反馈 (Feedback)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| feedback_id | UUID | 是 | 反馈唯一标识 |
| session_id | UUID | 是 | 关联的会话ID |
| tenant_id | UUID | 是 | 所属租户ID |
| rating | Integer | 是 | 评分(1-5) |
| comment | Text | 否 | 评价文字 |
| user_id | String(255) | 否 | 用户标识 |
| user_agent | Text | 否 | 用户代理信息 |
| ip_address | String(45) | 否 | IP地址(脱敏) |
| tags | Array[String] | 否 | 标签列表 |
| created_at | DateTime | 是 | 创建时间 |
| updated_at | DateTime | 是 | 更新时间 |

## 4. 统计数据 (Statistics)

| 字段名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| stat_id | UUID | 是 | 统计记录ID |
| tenant_id | UUID | 是 | 所属租户ID |
| stat_type | String(20) | 是 | 统计类型(daily/weekly/monthly) |
| date | Date | 是 | 统计日期 |
| total_sessions | Integer | 是 | 总会话数 |
| total_feedback | Integer | 是 | 总反馈数 |
| average_rating | Float | 是 | 平均评分 |
| rating_distribution | JSON | 是 | 评分分布 {"1":10, "2":20, ...} |
| language_distribution | JSON | 是 | 语言分布 {"python":50, "js":30, ...} |
| framework_distribution | JSON | 是 | 框架分布 {"django":20, "react":15, ...} |
| created_at | DateTime | 是 | 创建时间 |

## 5. 实体关系

```
Tenant (1) -----> (N) QASession
Tenant (1) -----> (N) Feedback  
Tenant (1) -----> (N) Statistics

QASession (1) -----> (N) Feedback
```

## 6. 核心约束

### 6.1 数据约束
- `rating` 必须在 1-5 之间
- `tenant_id` + `stat_type` + `date` 在统计表中唯一
- `api_key` 在租户表中唯一
- `session_id` 在问答会话表中唯一
- `feedback_id` 在反馈表中唯一

### 6.2 业务约束
- 每个会话可以有多个反馈（支持修改评分）
- 统计数据按租户隔离
- 软删除策略（通过 `is_active` 字段）

## 7. 索引建议

### 7.1 租户表
- `api_key` (唯一索引)
- `is_active`

### 7.2 问答会话表
- `tenant_id`
- `created_at`
- `language`
- `content_hash`
- `source`

### 7.3 反馈表
- `tenant_id`
- `session_id`
- `rating`
- `created_at`
- `user_id`

### 7.4 统计表
- `tenant_id`
- `date`
- `stat_type`
- `(tenant_id, stat_type, date)` (复合唯一索引)

## 8. 数据类型说明

| 类型 | 说明 | 示例 |
|------|------|------|
| UUID | 通用唯一标识符 | 550e8400-e29b-41d4-a716-************ |
| String(N) | 最大长度N的字符串 | "CodeStandardMCP" |
| Text | 长文本，无长度限制 | "这是一个很长的回答..." |
| Integer | 32位整数 | 42 |
| Float | 浮点数 | 4.25 |
| Boolean | 布尔值 | true/false |
| DateTime | 带时区的日期时间 | 2024-01-01T12:00:00Z |
| Date | 日期 | 2024-01-01 |
| JSON | JSON对象 | {"key": "value"} |
| Array[String] | 字符串数组 | ["tag1", "tag2"] |

这个简化版本专注于实体的核心属性定义，去除了具体的实现细节，便于理解和设计讨论。
