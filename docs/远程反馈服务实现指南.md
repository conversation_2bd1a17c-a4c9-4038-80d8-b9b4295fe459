# 远程反馈服务实现指南

## 快速开始

### 1. 最小可行产品 (MVP) 实现

如果您希望快速实现一个基础版本，可以先实现以下核心功能：

#### 1.1 简化的数据结构
```sql
-- 简化版租户表
CREATE TABLE tenants (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- 简化版问答会话表
CREATE TABLE qa_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    language VARCHAR(50),
    framework VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

-- 简化版反馈表
CREATE TABLE feedback (
    feedback_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES qa_sessions(session_id),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    rating INTEGER CHECK(rating >= 1 AND rating <= 5) NOT NULL,
    comment TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);
```

#### 1.2 核心API端点
- `POST /api/v1/qa-sessions` - 创建问答会话
- `GET /api/v1/qa-sessions/{session_id}` - 获取会话详情
- `POST /api/v1/feedback` - 提交反馈
- `GET /api/v1/health` - 健康检查

### 2. FastAPI 实现示例

#### 2.1 项目结构
```
feedback-service/
├── app/
│   ├── __init__.py
│   ├── main.py
│   ├── models/
│   │   ├── __init__.py
│   │   ├── qa_session.py
│   │   └── feedback.py
│   ├── api/
│   │   ├── __init__.py
│   │   ├── qa_sessions.py
│   │   └── feedback.py
│   ├── database.py
│   └── config.py
├── requirements.txt
├── Dockerfile
└── docker-compose.yml
```

#### 2.2 核心代码示例

**requirements.txt**
```
fastapi==0.104.1
uvicorn==0.24.0
asyncpg==0.29.0
pydantic==2.5.0
python-multipart==0.0.6
```

**app/config.py**
```python
from pydantic_settings import BaseSettings

class Settings(BaseSettings):
    database_url: str = "postgresql://user:password@localhost/feedback_service"
    api_key_salt: str = "your-secret-salt"
    
    class Config:
        env_file = ".env"

settings = Settings()
```

**app/database.py**
```python
import asyncpg
from typing import Optional

class Database:
    def __init__(self, database_url: str):
        self.database_url = database_url
        self.pool: Optional[asyncpg.Pool] = None
    
    async def connect(self):
        self.pool = await asyncpg.create_pool(self.database_url)
    
    async def disconnect(self):
        if self.pool:
            await self.pool.close()
    
    async def execute(self, query: str, *args):
        async with self.pool.acquire() as conn:
            return await conn.execute(query, *args)
    
    async def fetch(self, query: str, *args):
        async with self.pool.acquire() as conn:
            return await conn.fetch(query, *args)
    
    async def fetchrow(self, query: str, *args):
        async with self.pool.acquire() as conn:
            return await conn.fetchrow(query, *args)

db = Database(settings.database_url)
```

**app/models/qa_session.py**
```python
from pydantic import BaseModel
from typing import Optional
from datetime import datetime
import uuid

class QASessionCreate(BaseModel):
    question: str
    answer: str
    language: Optional[str] = None
    framework: Optional[str] = None

class QASessionResponse(BaseModel):
    session_id: str
    tenant_id: str
    question: str
    answer: str
    language: Optional[str] = None
    framework: Optional[str] = None
    created_at: datetime
```

**app/models/feedback.py**
```python
from pydantic import BaseModel, Field
from typing import Optional
from datetime import datetime

class FeedbackCreate(BaseModel):
    session_id: str
    rating: int = Field(..., ge=1, le=5)
    comment: Optional[str] = None

class FeedbackResponse(BaseModel):
    feedback_id: str
    session_id: str
    tenant_id: str
    rating: int
    comment: Optional[str] = None
    created_at: datetime
```

**app/api/qa_sessions.py**
```python
from fastapi import APIRouter, HTTPException, Depends
from app.models.qa_session import QASessionCreate, QASessionResponse
from app.database import db
import uuid

router = APIRouter(prefix="/api/v1/qa-sessions", tags=["qa-sessions"])

async def get_tenant_id(api_key: str = Depends(get_api_key)) -> str:
    # 简化版：直接返回固定租户ID
    return "default-tenant"

@router.post("/", response_model=dict)
async def create_qa_session(
    session_data: QASessionCreate,
    tenant_id: str = Depends(get_tenant_id)
):
    session_id = str(uuid.uuid4())
    
    await db.execute(
        """
        INSERT INTO qa_sessions (session_id, tenant_id, question, answer, language, framework)
        VALUES ($1, $2, $3, $4, $5, $6)
        """,
        session_id, tenant_id, session_data.question, session_data.answer,
        session_data.language, session_data.framework
    )
    
    return {
        "success": True,
        "data": {"session_id": session_id}
    }

@router.get("/{session_id}", response_model=dict)
async def get_qa_session(session_id: str):
    row = await db.fetchrow(
        "SELECT * FROM qa_sessions WHERE session_id = $1",
        session_id
    )
    
    if not row:
        raise HTTPException(status_code=404, detail="Session not found")
    
    return {
        "success": True,
        "data": dict(row)
    }
```

**app/main.py**
```python
from fastapi import FastAPI, Depends, HTTPException, Header
from app.database import db
from app.api import qa_sessions, feedback
from app.config import settings

app = FastAPI(title="Feedback Service", version="1.0.0")

# 简化的API密钥验证
async def get_api_key(authorization: str = Header(None)) -> str:
    if not authorization or not authorization.startswith("Bearer "):
        raise HTTPException(status_code=401, detail="Invalid API key")
    return authorization[7:]  # Remove "Bearer " prefix

@app.on_event("startup")
async def startup():
    await db.connect()

@app.on_event("shutdown")
async def shutdown():
    await db.disconnect()

@app.get("/api/v1/health")
async def health_check():
    return {
        "success": True,
        "data": {
            "status": "healthy",
            "version": "1.0.0"
        }
    }

# 注册路由
app.include_router(qa_sessions.router)
app.include_router(feedback.router)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8080)
```

### 3. 部署步骤

#### 3.1 使用 Docker Compose
```yaml
version: '3.8'

services:
  feedback-service:
    build: .
    ports:
      - "8080:8080"
    environment:
      - DATABASE_URL=**************************************/feedback_service
    depends_on:
      - db

  db:
    image: postgres:15
    environment:
      - POSTGRES_DB=feedback_service
      - POSTGRES_USER=postgres
      - POSTGRES_PASSWORD=password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"

volumes:
  postgres_data:
```

#### 3.2 启动服务
```bash
# 1. 克隆或创建项目
mkdir feedback-service && cd feedback-service

# 2. 创建上述文件结构和代码

# 3. 启动服务
docker-compose up -d

# 4. 初始化数据库
docker-compose exec db psql -U postgres -d feedback_service -c "
CREATE TABLE tenants (
    tenant_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) NOT NULL,
    api_key VARCHAR(255) UNIQUE NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE qa_sessions (
    session_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    language VARCHAR(50),
    framework VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW()
);

CREATE TABLE feedback (
    feedback_id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    session_id UUID NOT NULL REFERENCES qa_sessions(session_id),
    tenant_id UUID NOT NULL REFERENCES tenants(tenant_id),
    rating INTEGER CHECK(rating >= 1 AND rating <= 5) NOT NULL,
    comment TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

INSERT INTO tenants (name, api_key) VALUES ('Default Tenant', 'test-api-key-123');
"

# 5. 测试服务
curl -X GET http://localhost:8080/api/v1/health
```

### 4. 客户端修改

修改 CodeStandardMCP 的配置，将远程数据库URL改为HTTP API：

```python
# config/settings.py
self.remote_feedback_service_url = os.getenv("CSMCP_REMOTE_FEEDBACK_SERVICE_URL", "http://localhost:8080")
self.remote_feedback_api_key = os.getenv("CSMCP_REMOTE_FEEDBACK_API_KEY", "test-api-key-123")
```

这个实现指南提供了一个可以快速部署的基础版本，您可以根据需要逐步添加更多功能。
