# CodeStandardMCP - 编码规范服务工具设计文档

## 1. 项目概述

**项目名称**：CodeStandardMCP

**项目背景**：作为FastMCP框架的一个应用实例，CodeStandardMCP旨在为AI编码工具提供规范查询服务。在AI辅助编程日益普及的背景下，确保AI生成的代码符合行业标准和最佳实践变得尤为重要。

**项目目标**：构建一个专注于"规范即服务"(Standards as a Service)的MCP服务器，为各种AI编码工具提供编码规范查询服务，帮助它们生成高质量、符合标准的代码。

**核心功能**：

- 从外部接口获取各种编程语言和框架的编码规范
- 将规范缓存到本地文件系统以提高性能

## 2. 工具设计

### 2.1 工具概览

CodeStandardMCP将提供以下核心工具：

1. **StandardFetcher** - 编码规范获取工具
2. **StandardsManager** - 规范管理和更新工具

### 2.2 StandardSelector 结构

为了实现可扩展的规范选择，我们设计一个通用的选择器结构：

```
StandardSelector {
    language: 编程语言
    framework: 框架名称(可选)
    version: 版本信息(可选)
    environment: 环境类型(可选，如"服务端"、"客户端")
    domain: 编码细节(可选，如"具体实现细节"、"最佳实践")
    // 未来可能添加的其他选择条件...
}
```

这个结构允许我们根据多种条件来定位和选择特定的编码规范，并可以随着需求变化轻松扩展。

### 2.3 StandardFetcher 工具

**目的**：从指定的外部API获取最新的编码规范，并将其缓存到本地文件系统。

**接口设计**：

- **fetch_standard**
  - 参数：
    - `selector`: StandardSelector类型，包含规范选择条件
    - `force_refresh`: 是否强制从远程获取最新规范
  - 返回：包含编码规范内容的字符串

**功能说明**：

- 根据提供的选择器，构建API请求URL
- 检查本地缓存是否存在有效的规范数据
- 如果缓存无效或强制刷新，则从远程API获取最新规范
- 将获取的规范保存到本地缓存
- 返回规范内容

**错误处理**：

- API连接失败时，尝试使用本地缓存
- 本地缓存不存在时，返回适当的错误信息

### 2.4 StandardsManager 工具

**目的**：管理本地缓存的编码规范，包括更新和清理功能。

**接口设计**：

- **update_standards**
  - 参数：
    - `selector`: 可选的StandardSelector类型，用于筛选需要更新的规范
    - `force_all`: 是否强制更新所有规范
  - 返回：更新结果摘要

- **clean_cache**
  - 参数：
    - `older_than_days`: 清理多少天前的缓存
  - 返回：清理结果摘要

**功能说明**：

- 定期或按需更新本地缓存的编码规范
- 清理过期或不再需要的缓存文件
- 维护缓存文件的基本元数据

## 3. 数据结构设计

### 3.1 规范数据结构

编码规范数据将以简单文本或JSON格式存储，包含规范内容和基本元数据：

```
{
  "metadata": {
    "selector": {
      "language": "编程语言名称",
      "framework": "框架名称(可选)",
      "version": "版本号(可选)",
      "environment": "环境类型(可选)",
      "domain": "应用领域(可选)"
    },
    "fetch_date": "获取日期"
  },
  "content": "规范内容文本"
}
```

### 3.2 缓存管理数据结构

缓存管理使用简单的文件系统结构：

```
standards_cache/
├── <cache_key_1>.json
├── <cache_key_2>.json
└── cache_index.json        # 缓存索引文件
```

其中`cache_key`是根据StandardSelector生成的唯一标识符。

缓存索引文件结构：

```
{
  "entries": {
    "<cache_key_1>": {
      "path": "<cache_key_1>.json",
      "selector": {
        "language": "python",
        "framework": "django",
        ...
      },
      "last_updated": "2023-10-15T14:30:00Z",
      "size": 12345
    },
    "<cache_key_2>": {
      "path": "<cache_key_2>.json",
      "selector": {
        "language": "javascript",
        "framework": "react",
        ...
      },
      "last_updated": "2023-10-10T10:15:00Z",
      "size": 8765
    }
    // 更多条目...
  },
  "last_update_check": "2023-10-15T14:30:00Z"
}
```

## 4. 工作流程设计

### 4.1 规范获取流程

1. 客户端提供StandardSelector和是否强制刷新的选项
2. 系统根据StandardSelector生成缓存键
3. 检查本地缓存是否存在该键对应的文件
   a. 如果缓存存在且不需要强制刷新，读取并返回缓存内容
   b. 如果缓存不存在或需要强制刷新，进入步骤4
4. 构建API请求，从远程服务获取最新规范
5. 将获取的规范保存到本地缓存文件
6. 更新缓存索引
7. 返回规范内容给客户端

### 4.2 规范更新流程

1. 触发规范更新（定时或手动）
2. 系统加载缓存索引
3. 如果提供了选择器，筛选符合条件的缓存条目
4. 对于需要更新的每个规范：
   a. 从远程API获取最新规范
   b. 更新本地缓存文件
   c. 更新缓存索引中的时间戳
5. 保存更新后的缓存索引
6. 返回更新结果摘要

### 4.3 缓存清理流程

1. 触发缓存清理（定时或手动）
2. 系统加载缓存索引
3. 检查每个缓存条目的最后更新时间
4. 删除超过指定天数的缓存文件
5. 从缓存索引中移除对应条目
6. 保存更新后的缓存索引
7. 返回清理结果摘要

## 5. 接口定义

### 5.1 StandardFetcher 接口

```
fetch_standard(selector, force_refresh)
- 输入:
  - selector: StandardSelector对象，包含规范选择条件
  - force_refresh: 是否强制刷新，布尔值
- 输出:
  - 规范内容，字符串
  - 错误信息，如果获取失败
```

### 5.2 StandardsManager 接口

```
update_standards(selector, force_all)
- 输入:
  - selector: StandardSelector对象(可选)，用于筛选需要更新的规范
  - force_all: 是否强制更新所有，布尔值
- 输出:
  - 更新结果摘要，包含更新条目数和状态

clean_cache(older_than_days)
- 输入:
  - older_than_days: 天数阈值，整数
- 输出:
  - 清理结果摘要，包含清理条目数和释放空间
```

## 6. 实现建议

1. **可扩展性设计**：使用StandardSelector结构确保系统可以随着需求变化轻松添加新的选择条件

2. **缓存键生成**：实现一个稳定的算法，基于StandardSelector生成唯一的缓存键

3. **文件存储**：使用简单的文件系统存储缓存内容，避免引入数据库依赖

4. **错误处理**：实现简单的重试机制和优雅降级策略，确保服务稳定性

5. **配置管理**：提供简单的配置文件，允许调整缓存目录、API端点等参数

## 7. 总结

CodeStandardMCP是一个简单实用的编码规范服务工具，专注于从外部API获取编码规范并提供本地缓存功能。通过两个核心工具StandardFetcher和StandardsManager，它实现了规范的获取、缓存和管理功能。

该设计的核心优势在于通过StandardSelector结构提供了可扩展的规范选择机制，允许系统随着需求演进而灵活地添加新的选择条件，同时保持接口的一致性和稳定性。这种设计使得该服务能够适应更广泛的使用场景，为各种AI编码工具提供规范参考服务。