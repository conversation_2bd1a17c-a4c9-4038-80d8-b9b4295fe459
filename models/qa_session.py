"""
问答会话数据模型

定义问答会话相关的数据结构，用于跟踪用户问题和AI回答
"""

import uuid
from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field


class QASessionData(BaseModel):
    """问答会话数据模型"""
    session_id: str = Field(..., description="会话ID")
    question: str = Field(..., description="用户问题")
    answer: str = Field(..., description="AI回答")
    language: Optional[str] = Field(None, description="编程语言")
    framework: Optional[str] = Field(None, description="框架名称")
    version: Optional[str] = Field(None, description="版本信息")
    question_detail: Optional[str] = Field(None, description="具体问题内容")
    source: str = Field(default="remote", description="回答来源：remote/cache")
    created_at: datetime = Field(default_factory=datetime.now, description="创建时间")
    content_hash: Optional[str] = Field(None, description="内容哈希，用于去重")

    class Config:
        from_attributes = True

    @classmethod
    def generate_session_id(cls) -> str:
        """生成唯一的会话ID"""
        return str(uuid.uuid4())

    def to_dict(self) -> dict:
        """转换为字典格式"""
        return {
            "session_id": self.session_id,
            "question": self.question,
            "answer": self.answer,
            "language": self.language,
            "framework": self.framework,
            "version": self.version,
            "question_detail": self.question_detail,
            "source": self.source,
            "created_at": self.created_at.isoformat(),
            "content_hash": self.content_hash
        }


class QASessionRequest(BaseModel):
    """问答会话请求模型"""
    language: str
    framework: Optional[str] = None
    version: Optional[str] = None
    question: Optional[str] = None
    answer: str
    source: str = "remote"
    content_hash: Optional[str] = None

    def to_qa_session(self) -> QASessionData:
        """转换为QASessionData"""
        # 构建完整的问题描述
        question_parts = [f"{self.language}编程语言"]
        if self.framework:
            question_parts.append(f"{self.framework}框架")
        if self.version:
            question_parts.append(f"版本{self.version}")
        if self.question:
            question_parts.append(f"关于{self.question}")
        else:
            question_parts.append("的编码规范")
        
        full_question = "".join(question_parts)
        
        return QASessionData(
            session_id=QASessionData.generate_session_id(),
            question=full_question,
            answer=self.answer,
            language=self.language,
            framework=self.framework,
            version=self.version,
            question_detail=self.question,
            source=self.source,
            content_hash=self.content_hash
        )
