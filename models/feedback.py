"""
反馈数据模型

定义反馈相关的数据结构和验证规则
"""

from datetime import datetime
from typing import Optional
from pydantic import BaseModel, Field, validator


class FeedbackSubmission(BaseModel):
    """反馈提交请求模型"""
    question: str = Field(..., description="用户的问题", min_length=1, max_length=5000)
    answer: str = Field(..., description="AI的回答", min_length=1, max_length=10000)
    rating: int = Field(..., description="用户评分(1-5)", ge=1, le=5)
    comment: Optional[str] = Field(None, description="用户评价文字", max_length=2000)
    session_id: Optional[str] = Field(None, description="会话ID", max_length=100)
    user_id: Optional[str] = Field(None, description="用户ID", max_length=100)

    @validator('question', 'answer')
    def validate_text_content(cls, v):
        """验证文本内容不能为空白"""
        if not v or not v.strip():
            raise ValueError('内容不能为空')
        return v.strip()

    @validator('comment')
    def validate_comment(cls, v):
        """验证评价内容"""
        if v is not None:
            v = v.strip()
            if not v:
                return None
        return v


class FeedbackData(BaseModel):
    """反馈数据模型（数据库存储）"""
    id: Optional[int] = None
    question: str
    answer: str
    rating: int
    comment: Optional[str] = None
    session_id: Optional[str] = None
    user_id: Optional[str] = None
    created_at: datetime
    synced_at: Optional[datetime] = None
    sync_status: str = "pending"  # pending, synced, failed

    class Config:
        from_attributes = True


class FeedbackStats(BaseModel):
    """反馈统计模型"""
    total_count: int = 0
    average_rating: float = 0.0
    rating_distribution: dict[int, int] = Field(default_factory=dict)
    pending_sync_count: int = 0
    failed_sync_count: int = 0
    last_sync_time: Optional[datetime] = None

    class Config:
        from_attributes = True


class SyncResult(BaseModel):
    """同步结果模型"""
    success: bool
    synced_count: int = 0
    failed_count: int = 0
    error_message: Optional[str] = None
    sync_time: datetime

    class Config:
        from_attributes = True
