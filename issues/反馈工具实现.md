# 反馈工具实现任务

## 任务概述
为CodeStandardMCP项目添加反馈工具，支持用户对AI回答进行评价，并将问题、回答和评价存储到本地和远程SQLite数据库中。

## 实现方案
采用**本地存储 + 直接远程SQLite数据库同步**的混合方案：
- 本地SQLite数据库存储反馈数据，保证性能
- 实时或批量同步到远程SQLite数据库
- 支持离线使用和网络故障恢复

## 已完成的实现步骤

### 1. 依赖管理 ✅
- 文件：`pyproject.toml`
- 添加：`aiosqlite>=0.19.0` 依赖

### 2. 数据模型设计 ✅
- 文件：`models/feedback.py`
- 实现：
  - `FeedbackSubmission` - 反馈提交请求模型
  - `FeedbackData` - 反馈数据模型（数据库存储）
  - `FeedbackStats` - 反馈统计模型
  - `SyncResult` - 同步结果模型

### 3. 数据库管理器 ✅
- 文件：`storage/database_manager.py`
- 功能：
  - SQLite连接管理（本地+远程）
  - 表结构自动初始化
  - 异步数据库操作
  - 连接测试功能

### 4. 配置更新 ✅
- 文件：`config/settings.py`
- 新增配置：
  - 本地数据库路径：`local_feedback_db_path`
  - 远程数据库路径：`remote_feedback_db_path`
  - 同步开关：`feedback_sync_enabled`
  - 重试配置：`feedback_sync_retry_count`、`feedback_sync_timeout`

### 5. 反馈服务 ✅
- 文件：`services/feedback_service.py`
- 功能：
  - 反馈提交和本地存储
  - 单个反馈实时同步
  - 批量反馈同步
  - 反馈统计信息生成
  - 数据库连接测试

### 6. MCP工具实现 ✅
- 文件：`main.py`
- 新增工具：
  - `submit_feedback` - 提交反馈（自动同步到远程数据库）

### 6.1. 工具优化 ✅
- 移除不必要的工具暴露：
  - 移除 `sync_feedback` - 改为内部自动同步
  - 移除 `get_feedback_stats` - 改为内部管理功能
- 简化用户界面，只保留核心的`submit_feedback`工具

### 7. 模块导入更新 ✅
- 文件：`__init__.py`
- 导出新的反馈相关模块

### 8. 问答会话跟踪 ✅
- 文件：`models/qa_session.py` - 问答会话数据模型
- 文件：`services/qa_session_service.py` - 问答会话服务
- 功能：
  - 自动生成session_id
  - 记录问题、回答和相关元数据
  - 支持基于session_id的反馈关联

### 9. fetch_standard工具增强 ✅
- 修改：`main.py`中的`fetch_standard`工具
- 新增功能：
  - 自动创建问答会话记录
  - 返回结果中包含session_id
  - 支持问答内容的完整跟踪

### 10. submit_feedback工具优化 ✅
- 修改：`main.py`中的`submit_feedback`工具
- 新增功能：
  - 支持session_id自动关联问答内容
  - 保持向后兼容，仍支持手动输入
  - 智能参数验证和错误处理

## 数据库表结构

### 反馈表
```sql
CREATE TABLE feedback (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    rating INTEGER CHECK(rating >= 1 AND rating <= 5) NOT NULL,
    comment TEXT,
    session_id TEXT,
    user_id TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    synced_at TIMESTAMP,
    sync_status TEXT DEFAULT 'pending'
);

-- 索引
CREATE INDEX idx_feedback_sync_status ON feedback(sync_status);
CREATE INDEX idx_feedback_created_at ON feedback(created_at);
CREATE INDEX idx_feedback_session_id ON feedback(session_id);
```

### 问答会话表
```sql
CREATE TABLE qa_sessions (
    session_id TEXT PRIMARY KEY,
    question TEXT NOT NULL,
    answer TEXT NOT NULL,
    language TEXT,
    framework TEXT,
    version TEXT,
    question_detail TEXT,
    source TEXT DEFAULT 'remote',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    content_hash TEXT
);

-- 索引
CREATE INDEX idx_qa_sessions_created_at ON qa_sessions(created_at);
CREATE INDEX idx_qa_sessions_language ON qa_sessions(language);
CREATE INDEX idx_qa_sessions_content_hash ON qa_sessions(content_hash);
```

## 环境变量配置

```bash
# 远程数据库路径（可选）
CSMCP_REMOTE_FEEDBACK_DB_PATH=/path/to/remote/feedback.db

# 同步配置
CSMCP_FEEDBACK_SYNC_ENABLED=true
CSMCP_FEEDBACK_SYNC_RETRY_COUNT=3
CSMCP_FEEDBACK_SYNC_TIMEOUT=30
```

## 使用示例

### 1. 获取编码规范（自动生成session_id）
```python
result = await mcp_client.call_tool("fetch_standard", {
    "language": "python",
    "framework": "django",
    "question": "模型字段命名规范"
})

# 返回结果包含session_id
session_id = result["session_id"]  # 例如: "abc123-def456-789"
```

### 2. 提交反馈（推荐方式：使用session_id）
```python
result = await mcp_client.call_tool("submit_feedback", {
    "rating": 5,
    "comment": "回答很详细，很有帮助！",
    "session_id": session_id,  # 自动关联问答内容
    "user_id": "user_456"
})
```

### 3. 提交反馈（兼容方式：手动提供问答内容）
```python
result = await mcp_client.call_tool("submit_feedback", {
    "rating": 4,
    "comment": "不错的回答",
    "question": "Python最佳实践有哪些？",
    "answer": "Python最佳实践包括：1. 遵循PEP8规范...",
    "user_id": "user_456"
})
```

### 4. 查看反馈统计（内部管理功能）
反馈统计信息通过日志记录，管理员可以直接查询数据库获取详细统计。

## 同步策略

1. **实时同步**：每次提交反馈后自动尝试同步到远程数据库
2. **批量同步**：通过`sync_feedback`工具手动触发批量同步
3. **失败重试**：网络失败时标记为`failed`状态，可重新同步
4. **状态跟踪**：`pending`（待同步）、`synced`（已同步）、`failed`（同步失败）

## 错误处理

- 网络连接失败：本地存储成功，远程同步失败时标记状态
- 数据验证错误：Pydantic模型自动验证输入数据
- 数据库锁定：使用异步操作避免阻塞
- 同步冲突：远程数据库独立存储，避免ID冲突

## 测试建议

1. 测试本地数据库操作
2. 测试远程数据库连接和同步
3. 测试网络故障恢复
4. 测试数据验证和错误处理
5. 测试统计信息准确性

## 后续优化方向

1. 添加数据库迁移机制
2. 实现更复杂的冲突解决策略
3. 添加反馈数据导出功能
4. 实现反馈数据分析和报告
5. 添加反馈数据备份和恢复功能
