#!/usr/bin/env python3
"""
CodeStandardMCP - 编码规范信息即服务

一个基于FastMCP 2.0的编码规范信息管理服务器，提供编码规范信息获取、管理等功能。
"""

import asyncio
import json
import logging
import sys
from datetime import datetime
from typing import Any, Dict, List, Optional

# FastMCP 2.0 导入
from fastmcp import FastMCP

# 导入核心模块
from config.settings import config
from models.standard import StandardSelector
from models.feedback import FeedbackSubmission
from models.qa_session import QASessionRequest
from services.standard_fetcher import standard_fetcher
from services.standards_manager import standards_manager
from services.feedback_service import feedback_service
from services.qa_session_service import qa_session_service


def _create_error_response(error_type: str, message: str, **kwargs) -> dict:
    """创建统一格式的错误响应"""
    return {
        "success": False,
        "error": message,
        "error_type": error_type,
        **kwargs
    }


async def _handle_timeout_with_cache_fallback(fetcher, selector, force_refresh: bool, request_id: str) -> dict:
    """处理超时情况，尝试缓存降级"""

    # 如果不是强制刷新，尝试快速获取缓存
    if not force_refresh:
        try:
            # 使用更短的超时来确保缓存获取不会再次阻塞
            cached_result = await asyncio.wait_for(
                fetcher.fetch_standard(selector, force_refresh=False),
                timeout=5.0  # 缓存获取最多5秒
            )
            if cached_result.get('success') and cached_result.get('source') == 'cache':
                logger.info(f"✅ Using cached content for {request_id[:8]}")
                return cached_result
        except (asyncio.TimeoutError, Exception) as e:
            logger.warning(f"⚠️ Cache fallback failed for {request_id[:8]}: {e}")

    return _create_error_response(
        "timeout",
        "API请求超时（90秒），请稍后重试或检查网络连接",
        timeout_seconds=90
    )

# 配置日志
# 确保日志输出到文件（如果没有设置环境变量，使用默认路径）
log_file_path = config.log_file or "log/app.log"

# 确保日志目录存在
import os
os.makedirs(os.path.dirname(log_file_path), exist_ok=True)

logging.basicConfig(
    level=getattr(logging, config.log_level.upper()),
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
    handlers=[
        logging.StreamHandler(sys.stdout),
        logging.FileHandler(log_file_path, encoding='utf-8'),
    ],
)

logger = logging.getLogger("CodeStandardMCP")


def create_error_response(
    error_type: str, message: str, **context
) -> Dict[str, Any]:
    """创建规范化的错误响应"""
    return {
        "success": False,
        "error": {
            "type": error_type,
            "message": message if not config.mask_error_details else "操作失败",
            "context": context if not config.mask_error_details else {},
        },
        "timestamp": "2024-01-01T00:00:00Z",  # 简化时间戳
    }


# 创建FastMCP应用实例
mcp = FastMCP(
    name=config.name,
    instructions="""
    CodeStandardMCP - 编码信息即服务

    这是一个简化的编码信息管理MCP服务器，提供以下核心功能：

    🔍 规范信息获取 (StandardFetcher)：
    - fetch_standard: 从外部API获取编码规范信息并缓存到本地

    🛠️ 规范信息管理 (StandardsManager)：
    - update_standards: 更新本地规范信息数据
    - clean_cache: 清理缓存和临时文件

    📝 反馈管理 (FeedbackService)：
    - submit_feedback: 提交用户反馈和评价（自动同步到远程数据库）
    """,
    dependencies=[
        "httpx>=0.25.0",
        "pydantic>=2.0.0",
    ],
)

# ==================== 日志辅助函数 ====================

def _log_tool_call_params(tool_name: str, **kwargs) -> None:
    """记录工具调用参数"""
    try:
        if not config.should_log_api_details():
            return

        # 过滤掉None值和内部参数
        filtered_params = {k: v for k, v in kwargs.items() if v is not None and not k.startswith('_')}

        # 构建参数字符串
        param_parts = []
        for key, value in filtered_params.items():
            if key == 'force_refresh' and not value:
                continue  # 跳过默认的false值
            param_parts.append(f"{key}={value}")

        param_str = " | ".join(param_parts) if param_parts else "no_params"

        logger.info(f"🔧 [TOOL_CALL] Tool: {tool_name} | Params: {param_str}")
    except Exception as e:
        logger.debug(f"记录工具调用参数失败: {e}")

def _log_tool_result(tool_name: str, success: bool = None, **kwargs) -> None:
    """记录工具执行结果"""
    try:
        if not config.should_log_api_details():
            return

        # 构建结果信息
        result_parts = [f"Success: {success}"]

        for key, value in kwargs.items():
            if value is not None and not key.startswith('_'):
                if key == 'content_size':
                    result_parts.append(f"Content_Size: {value} chars")
                elif key == 'error':
                    error_preview = config.truncate_text(str(value), 50)
                    result_parts.append(f"Error: '{error_preview}'")
                else:
                    result_parts.append(f"{key.title()}: {value}")

        result_str = " | ".join(result_parts)

        status_icon = "✅" if success else "❌"
        logger.info(f"{status_icon} [TOOL_RESULT] Tool: {tool_name} | {result_str}")
    except Exception as e:
        logger.debug(f"记录工具执行结果失败: {e}")

# ==================== MCP 工具实现 ====================


@mcp.tool(tags={"public", "standards", "fetcher"})
async def fetch_standard(
    language: str,
    framework: Optional[str] = None,
    version: Optional[str] = None,
    question: Optional[str] = None,
    force_refresh: bool = False,
) -> str:
    """
    从外部API获取编码相关信息并缓存到本地

    这个工具从指定的外部API端点获取编码信息文档，支持多种编程语言和框架。
    获取的信息会被缓存到本地文件系统中以提高性能。
    此工具是单线程，并且响应时间较长，请耐心等候，不要重复调用。

    ⚠️ 重要使用指南：
    1. 问题必须清晰、具体、详细 - 避免模糊宽泛的问题
    2. 大任务需要拆解 - 将复杂任务分解为多个具体的小问题
    3. 原子化提问 - 每次只问一个具体方面，避免用连词连接多个子问题
    4. 一次只问一个问题 - 等待当前问题回答完成后再提出下一个问题
    5. 依次分别请求 - 按逻辑顺序逐步获取所需信息

    示例对比：
    ❌ 错误：question="Python最佳实践"（过于宽泛）
    ✅ 正确：question="Python函数命名规范的具体要求"（清晰具体）

    ❌ 错误：一次性问"Python Web开发的完整指南"（任务过大）
    ✅ 正确：分步骤问：
       1. "Python Django项目目录结构的标准规范"
       2. "Django模型设计的最佳实践"
       3. "Django视图函数的编码规范"

    ❌ 错误：同时询问多个方面"Python pytorch的具体实现方式和适用场景"（一问多答）
    ✅ 正确：原子化分步提问：
       1. 先问："Python pytorch的具体实现方式"
       2. 等回答完成后再问："Python pytorch的适用场景"
       3. 如需对比再问："Python tensorflow的具体实现方式"
       4. 最后问："选择pytorch还是tensorflow的判断标准"

    Args:
        language: 编程语言（如 "python", "javascript", "java" 等）
        framework: 可选的框架名称（如 "django", "react", "spring" 等）
        version: 可选的版本信息（如 "3.8", "ES2020" 等）
        question: 需要获取的信息内容 - 必须是具体、清晰、详细的问题（如 "电力运维周期聚合的最佳实践", "数据中心的数据计算流程", "xxx SDK的功能"等）
        force_refresh: 是否强制刷新，忽略现有缓存（默认：false）

    Returns:
        JSON格式的字符串，包含获取结果、规范信息和统计数据

    Examples:
        fetch_standard("python", "django", question="模型字段命名规范")
        fetch_standard("javascript", "react", question="组件状态管理的具体实现方式")
        fetch_standard("java", "spring", question="RESTful API错误处理的标准做法")
    """
    # 立即确认请求接收并开始处理
    request_id = f"{language}_{framework}_{hash(str((language, framework, version, question)))}"
    logger.info(f"✅ Processing {language}/{framework} - ID: {request_id[:8]}")

    # 记录工具调用参数
    _log_tool_call_params(
        tool_name="fetch_standard",
        language=language,
        framework=framework,
        version=version,
        question=question,
        force_refresh=force_refresh,
        request_id=request_id[:8]
    )

    try:

        # 创建规范信息选择器
        selector = StandardSelector(
            language=language,
            framework=framework,
            version=version,
            question=question,
        )

        # 添加MCP层面的超时保护（45秒）
        import time
        start_time = time.time()
        logger.info(f"🔄 [MCP] Starting fetch request {request_id[:8]} at {time.strftime('%H:%M:%S')}")
        logger.info(f"🔄 [MCP] Fetching from API (max 90s timeout)...")
        logger.info(f"🔄 [MCP] Parameters: language={language}, framework={framework}, force_refresh={force_refresh}")

        try:
            result = await asyncio.wait_for(
                standard_fetcher.fetch_standard(
                    selector=selector,
                    force_refresh=force_refresh,
                ),
                timeout=90.0
            )
            end_time = time.time()
            duration = end_time - start_time
            logger.info(f"✅ [MCP] Request {request_id[:8]} completed successfully in {duration:.2f}s")

            # 创建问答会话记录
            session_id = None
            if result.get("success") and result.get("content"):
                try:
                    # 初始化问答会话服务
                    await qa_session_service.initialize()

                    # 创建会话请求
                    session_request = QASessionRequest(
                        language=language,
                        framework=framework,
                        version=version,
                        question=question,
                        answer=result.get("content", ""),
                        source=result.get("source", "unknown")
                    )

                    # 创建会话
                    session_data = await qa_session_service.create_session(session_request)
                    session_id = session_data.session_id

                    logger.info(f"✅ 问答会话创建成功: {session_id}")

                except Exception as e:
                    logger.warning(f"⚠️ 创建问答会话失败: {e}")

            # 在返回结果中添加session_id
            if session_id:
                result["session_id"] = session_id

            # 记录工具执行结果
            _log_tool_result(
                tool_name="fetch_standard",
                success=result.get("success", False),
                source=result.get("source", "unknown"),
                content_size=len(result.get("content", "")),
                cached=result.get("cached", False),
                request_id=request_id[:8]
            )

        except asyncio.TimeoutError:
            end_time = time.time()
            duration = end_time - start_time
            logger.warning(f"⏰ [MCP] Request {request_id[:8]} timed out after {duration:.2f}s (limit: 90s)")
            logger.warning(f"⏰ [MCP] Timeout occurred at {time.strftime('%H:%M:%S')}")
            result = await _handle_timeout_with_cache_fallback(
                standard_fetcher, selector, force_refresh, request_id
            )

        logger.info(f"✅ Completed {request_id[:8]}")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ Request {request_id[:8]} failed: {str(e)}")

        # 记录工具执行失败结果
        _log_tool_result(
            tool_name="fetch_standard",
            success=False,
            error=str(e),
            request_id=request_id[:8]
        )

        error_response = _create_error_response(
            "tool_execution",
            f"Failed to fetch standard: {str(e)}",
            language=language,
            framework=framework,
            request_id=request_id[:8],
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "management", "update"})
async def update_standards(
    language: Optional[str] = None,
    framework: Optional[str] = None,
    version: Optional[str] = None,
    question: Optional[str] = None,
    force_all: bool = False,
) -> str:
    """
    更新本地规范信息数据

    这个工具更新本地存储的编码规范信息，可以选择性地更新特定语言/框架的规范信息，
    或者强制更新所有规范信息。

    Args:
        language: 可选的编程语言过滤器
        framework: 可选的框架过滤器
        version: 可选的版本过滤器
        question: 可选的信息内容过滤器
        force_all: 是否强制更新所有规范信息（默认：false）

    Returns:
        JSON格式的字符串，包含更新结果和统计信息

    Examples:
        update_standards()
        update_standards("python", "django")
        update_standards(force_all=true)
    """
    # 立即确认请求接收
    update_id = f"update_{hash(str((language, framework, force_all)))}"
    logger.info(f"✅ Update request received - ID: {update_id[:8]} | Language: {language} | Force all: {force_all}")

    try:
        logger.info(f"🔄 Processing update {update_id[:8]}...")

        # 创建可选的规范信息选择器
        selector = None
        if language:
            logger.info(f"📝 Creating selector for {language}/{framework}")
            selector = StandardSelector(
                language=language,
                framework=framework,
                version=version,
                question=question,
            )

        logger.info(f"🚀 Starting standards update for {update_id[:8]}...")
        result = await standards_manager.update_standards(
            selector=selector,
            force_all=force_all,
        )

        logger.info(f"✅ Update {update_id[:8]} completed successfully")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ Update {update_id[:8]} failed: {str(e)}")
        error_response = create_error_response(
            "tool_execution",
            f"Failed to update standards: {str(e)}",
            language=language,
            framework=framework,
            update_id=update_id[:8],
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


@mcp.tool(tags={"public", "management", "cache"})
async def clean_cache(older_than_days: int = 30) -> str:
    """
    清理缓存和临时文件

    这个工具清理过期的缓存文件和临时数据，释放磁盘空间。
    可以指定清理多少天前的缓存文件。

    Args:
        older_than_days: 清理多少天前的缓存（默认：30天）

    Returns:
        JSON格式的字符串，包含清理结果和统计信息

    Examples:
        clean_cache()
        clean_cache(7)  # 清理7天前的缓存
    """
    # 立即确认请求接收
    clean_id = f"clean_{hash(older_than_days)}"
    logger.info(f"✅ Cache clean request received - ID: {clean_id[:8]} | Days: {older_than_days}")

    try:
        logger.info(f"🧹 Starting cache cleanup for {clean_id[:8]}...")

        result = await standards_manager.clean_cache(
            older_than_days=older_than_days
        )

        logger.info(f"✅ Cache cleanup {clean_id[:8]} completed successfully")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ Cache cleanup {clean_id[:8]} failed: {str(e)}")
        error_response = create_error_response(
            "tool_execution",
            f"Failed to clean cache: {str(e)}",
            older_than_days=older_than_days,
            clean_id=clean_id[:8],
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


# ==================== 反馈管理工具 ====================
@mcp.tool(tags={"public", "feedback", "submit"})
async def submit_feedback(
    question: str,
    answer: str,
    rating: int,
    comment: Optional[str] = None,
    session_id: Optional[str] = None,
    user_id: Optional[str] = None,
) -> str:
    """
    提交用户反馈和评价

    收集用户对AI回答的评价，并将问题、回答和评价存储到本地SQLite数据库中。
    如果配置了远程数据库，会自动尝试同步到远程数据库。

    Args:
        question: 用户的问题（必填）
        answer: AI的回答（必填）
        rating: 用户评分，1-5分（必填）
        comment: 用户评价文字（可选）
        session_id: 会话ID，用于关联同一次对话（可选）
        user_id: 用户ID（可选）

    Returns:
        JSON格式的字符串，包含提交结果

    Examples:
        submit_feedback("Python最佳实践", "使用PEP8规范...", 5, "很有帮助")
        submit_feedback("如何优化代码", "可以使用缓存...", 4, session_id="chat_123")
    """
    # 立即确认请求接收
    feedback_id = f"feedback_{hash(str((question, answer, rating)))}"
    logger.info(f"✅ 反馈提交请求接收 - ID: {feedback_id[:8]} | 评分: {rating}")

    try:
        # 创建反馈提交对象
        submission = FeedbackSubmission(
            question=question,
            answer=answer,
            rating=rating,
            comment=comment,
            session_id=session_id,
            user_id=user_id
        )

        # 初始化反馈服务（如果尚未初始化）
        await feedback_service.initialize()

        # 提交反馈
        result = await feedback_service.submit_feedback(submission)

        logger.info(f"✅ 反馈提交完成 - ID: {feedback_id[:8]} | 成功: {result['success']}")
        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        logger.error(f"❌ 反馈提交失败 {feedback_id[:8]}: {str(e)}")
        error_response = create_error_response(
            "feedback_submission",
            f"反馈提交失败: {str(e)}",
            feedback_id=feedback_id[:8],
            rating=rating
        )
        return json.dumps(error_response, ensure_ascii=False, indent=2)


# ==================== 服务器启动 ====================

async def async_main():
    """异步主函数"""
    try:
        logger.info(f"启动 {config.name} v{config.version}")
        logger.info(f"数据目录: {config.data_dir}")
        logger.info(f"缓存目录: {config.cache_dir}")

        # 运行MCP服务器 - 使用异步方法
        await mcp.run_async()

    except KeyboardInterrupt:
        logger.info("服务器被用户停止")
    except Exception as e:
        logger.error(f"服务器错误: {e}")
        sys.exit(1)


def main():
    """同步入口点函数"""
    try:
        asyncio.run(async_main())
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Application error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
