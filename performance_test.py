
"""
CodeStandardMCP 基础性能测试脚本

对MCP服务的核心功能进行基础性能测试
"""

import asyncio
import json
import time
import statistics
import argparse
import sys
import os
from datetime import datetime
from typing import Dict, Any
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入服务类
from config.settings import config
from models.standard import StandardSelector
from services.standard_fetcher import StandardFetcher
from services.standards_manager import StandardsManager

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('performance_test.log', encoding='utf-8')
    ]
)
logger = logging.getLogger(__name__)


class PerformanceTestSuite:
    """MCP基础性能测试套件"""

    def __init__(self):
        self.results = {
            'basic_tests': []
        }
        self.start_time = None
        self.test_data = [
            {"language": "python", "framework": "django", "question": "模型字段命名规范"},
            {"language": "javascript", "framework": "react", "question": "组件状态管理最佳实践"},
            {"language": "python", "framework": None, "question": "函数命名规范"},
            {"language": "typescript", "framework": "vue", "question": "类型定义最佳实践"},
            {"language": "csharp", "framework": "dotnet", "question": "异步编程模式"}
        ]

        # 初始化服务实例
        self.standard_fetcher = StandardFetcher()
        self.standards_manager = StandardsManager()
    
    async def run_single_test(self, test_data: Dict[str, Any], test_id: str = "") -> Dict[str, Any]:
        """执行单次测试"""
        start_time = time.time()

        try:
            # 创建标准选择器
            selector = StandardSelector(
                language=test_data["language"],
                framework=test_data["framework"],
                version=test_data.get("version"),
                question=test_data["question"]
            )

            # 调用服务获取标准
            result = await self.standard_fetcher.fetch_standard(
                selector=selector,
                force_refresh=test_data.get("force_refresh", False)
            )

            end_time = time.time()
            duration = end_time - start_time

            success = result.get("success", False)
            content = result.get("content", "")
            content_size = len(content)
            source = result.get("source", "unknown")

            test_result = {
                "test_id": test_id,
                "duration": duration,
                "success": success,
                "content": content,
                "content_size": content_size,
                "source": source,
                "params": test_data,
                "timestamp": datetime.now().isoformat()
            }

            # 如果失败，添加错误信息
            if not success:
                test_result["error"] = result.get("error", "未知错误")

            return test_result

        except Exception as e:
            end_time = time.time()
            duration = end_time - start_time

            return {
                "test_id": test_id,
                "duration": duration,
                "success": False,
                "error": str(e),
                "params": test_data,
                "timestamp": datetime.now().isoformat()
            }
    
    async def basic_performance_test(self) -> None:
        """基础性能测试"""
        logger.info("🚀 开始基础性能测试...")

        for i, test_data in enumerate(self.test_data):
            logger.info(f"执行测试 {i+1}/{len(self.test_data)}: {test_data['language']}/{test_data.get('framework', 'None')}")

            # 执行单次测试
            test_data_copy = test_data.copy()
            test_data_copy['force_refresh'] = True  # 不使用缓存，测试真实性能

            result = await self.run_single_test(test_data_copy, f"basic_{i+1}")
            self.results['basic_tests'].append(result)

            if result['success']:
                logger.info(f"测试完成 - 耗时: {result['duration']:.2f}s, 成功: {result['success']}, 来源: {result.get('source', 'unknown')}")
            else:
                error_msg = result.get('error', '未知错误')
                logger.warning(f"测试失败 - 耗时: {result['duration']:.2f}s, 错误: {error_msg}")

            # 测试间隔，避免过于频繁的请求
            await asyncio.sleep(0.5)

    




    def generate_report(self) -> str:
        """生成基础性能测试报告"""
        report = []
        report.append("=" * 60)
        report.append("CodeStandardMCP 基础性能测试报告")
        report.append("=" * 60)
        report.append(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 基础性能测试结果
        if self.results['basic_tests']:
            report.append("📊 基础性能测试结果:")
            report.append("-" * 40)

            durations = [t['duration'] for t in self.results['basic_tests'] if t['success']]
            content_sizes = [t['content_size'] for t in self.results['basic_tests'] if t['success']]
            success_count = len([t for t in self.results['basic_tests'] if t['success']])
            total_count = len(self.results['basic_tests'])
            success_rate = (success_count / total_count * 100) if total_count > 0 else 0

            if durations:
                report.append(f"平均响应时间: {statistics.mean(durations):.2f}秒")
                report.append(f"最快响应时间: {min(durations):.2f}秒")
                report.append(f"最慢响应时间: {max(durations):.2f}秒")
                report.append(f"响应时间标准差: {statistics.stdev(durations):.2f}秒" if len(durations) > 1 else "响应时间标准差: 0.00秒")
            else:
                report.append("没有成功的测试结果")

            if content_sizes:
                report.append(f"平均回答字符数: {statistics.mean(content_sizes):.0f}字符")
                report.append(f"最短回答字符数: {min(content_sizes)}字符")
                report.append(f"最长回答字符数: {max(content_sizes)}字符")
                report.append(f"总回答字符数: {sum(content_sizes)}字符")

            report.append(f"成功率: {success_rate:.1f}% ({success_count}/{total_count})")
            report.append("")

            # 详细测试结果
            report.append("详细测试结果:")
            for i, test in enumerate(self.results['basic_tests'], 1):
                status = "✅" if test['success'] else "❌"
                params = test['params']
                framework = params.get('framework', 'None')
                content_size = test.get('content_size', 0)
                report.append(f"{i:2d}. {status} {params['language']}/{framework} - {test['duration']:.2f}s - {content_size}字符")
                if not test['success'] and 'error' in test:
                    report.append(f"     错误: {test['error']}")
            report.append("")

        # 性能建议
        report.append("💡 性能分析:")
        report.append("-" * 40)

        if self.results['basic_tests']:
            successful_durations = [t['duration'] for t in self.results['basic_tests'] if t['success']]
            if successful_durations:
                avg_duration = statistics.mean(successful_durations)
                if avg_duration > 5:
                    report.append("• 平均响应时间较长，建议优化API调用或增加缓存")
                elif avg_duration < 1:
                    report.append("• 响应时间良好，系统性能表现优秀")
                else:
                    report.append("• 响应时间正常，系统性能表现良好")

            success_rate = len([t for t in self.results['basic_tests'] if t['success']]) / len(self.results['basic_tests']) * 100
            if success_rate < 90:
                report.append("• 成功率较低，建议检查网络连接和API服务状态")
            elif success_rate == 100:
                report.append("• 所有测试均成功，系统稳定性良好")
            else:
                report.append("• 成功率良好，系统运行稳定")
        else:
            report.append("• 无测试结果，请检查测试配置")

        report.append("")
        report.append("=" * 60)

        return "\n".join(report)

    async def run_basic_test(self) -> None:
        """运行基础性能测试"""
        logger.info("🎯 开始MCP基础性能测试")
        self.start_time = time.time()

        try:
            # 基础性能测试
            await self.basic_performance_test()

            # 生成报告
            report = self.generate_report()

            # 保存报告到文件
            report_file = f"basic_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report)

            # 保存详细结果到JSON
            results_file = f"basic_performance_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(results_file, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, ensure_ascii=False, indent=2)

            # 输出报告
            print(report)

            total_time = time.time() - self.start_time
            logger.info(f"✅ 基础性能测试完成 - 总耗时: {total_time:.1f}秒")
            logger.info(f"📄 报告已保存到: {report_file}")
            logger.info(f"📊 详细结果已保存到: {results_file}")

        except Exception as e:
            logger.error(f"❌ 测试过程中发生错误: {str(e)}")
            raise


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="CodeStandardMCP 基础性能测试工具")
    parser.add_argument("--verbose", "-v", action="store_true",
                       help="显示详细日志")

    args = parser.parse_args()

    # 设置日志级别
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    # 创建测试套件
    test_suite = PerformanceTestSuite()

    try:
        await test_suite.run_basic_test()

    except KeyboardInterrupt:
        logger.info("⚠️ 测试被用户中断")
    except Exception as e:
        logger.error(f"❌ 测试失败: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    # 检查依赖
    try:
        from services.standard_fetcher import StandardFetcher
        logger.info("✅ MCP服务类导入成功")
    except ImportError as e:
        logger.error(f"❌ 无法导入MCP服务类: {e}")
        logger.error("请确保在CodeStandardMCP项目根目录下运行此脚本")
        sys.exit(1)

    # 运行测试
    asyncio.run(main())
