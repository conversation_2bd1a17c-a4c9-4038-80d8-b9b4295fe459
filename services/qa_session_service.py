"""
问答会话服务

提供问答会话的存储、查询和管理功能
"""

import hashlib
import logging
from datetime import datetime
from typing import Any, Dict, Optional

from models.qa_session import QASessionData, QASessionRequest
from storage.database_manager import database_manager

logger = logging.getLogger("CodeStandardMCP.QASessionService")


class QASessionService:
    """问答会话服务类"""

    def __init__(self):
        pass

    async def initialize(self):
        """初始化问答会话服务"""
        try:
            success = await database_manager.initialize_databases()
            if success:
                logger.info("问答会话服务初始化成功")
            else:
                logger.warning("问答会话服务初始化部分失败")
            return success
        except Exception as e:
            logger.error(f"问答会话服务初始化失败: {e}")
            return False

    async def create_session(self, request: QASessionRequest) -> QASessionData:
        """创建新的问答会话"""
        try:
            # 生成内容哈希
            content_hash = self._generate_content_hash(request.answer)
            request.content_hash = content_hash

            # 转换为会话数据
            session_data = request.to_qa_session()

            # 保存到数据库
            await self._save_session(session_data)

            logger.info(f"问答会话创建成功，session_id: {session_data.session_id}")
            return session_data

        except Exception as e:
            logger.error(f"创建问答会话失败: {e}")
            raise

    async def _save_session(self, session_data: QASessionData):
        """保存会话到数据库"""
        sql = """
        INSERT INTO qa_sessions (
            session_id, question, answer, language, framework, version, 
            question_detail, source, created_at, content_hash
        ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            session_data.session_id,
            session_data.question,
            session_data.answer,
            session_data.language,
            session_data.framework,
            session_data.version,
            session_data.question_detail,
            session_data.source,
            session_data.created_at.isoformat(),
            session_data.content_hash
        )
        
        await database_manager.execute_insert(sql, params, use_remote=False)

    async def get_session(self, session_id: str) -> Optional[QASessionData]:
        """根据session_id获取会话数据"""
        try:
            sql = "SELECT * FROM qa_sessions WHERE session_id = ?"
            rows = await database_manager.execute_query(sql, (session_id,), use_remote=False)
            
            if not rows:
                logger.warning(f"未找到会话: {session_id}")
                return None

            row = rows[0]
            return QASessionData(
                session_id=row['session_id'],
                question=row['question'],
                answer=row['answer'],
                language=row['language'],
                framework=row['framework'],
                version=row['version'],
                question_detail=row['question_detail'],
                source=row['source'],
                created_at=datetime.fromisoformat(row['created_at']),
                content_hash=row['content_hash']
            )

        except Exception as e:
            logger.error(f"获取会话数据失败: {e}")
            return None

    async def find_similar_session(self, content_hash: str) -> Optional[QASessionData]:
        """根据内容哈希查找相似的会话"""
        try:
            sql = """
            SELECT * FROM qa_sessions 
            WHERE content_hash = ? 
            ORDER BY created_at DESC 
            LIMIT 1
            """
            rows = await database_manager.execute_query(sql, (content_hash,), use_remote=False)
            
            if not rows:
                return None

            row = rows[0]
            return QASessionData(
                session_id=row['session_id'],
                question=row['question'],
                answer=row['answer'],
                language=row['language'],
                framework=row['framework'],
                version=row['version'],
                question_detail=row['question_detail'],
                source=row['source'],
                created_at=datetime.fromisoformat(row['created_at']),
                content_hash=row['content_hash']
            )

        except Exception as e:
            logger.error(f"查找相似会话失败: {e}")
            return None

    def _generate_content_hash(self, content: str) -> str:
        """生成内容哈希"""
        return hashlib.md5(content.encode('utf-8')).hexdigest()

    async def get_session_stats(self) -> Dict[str, Any]:
        """获取会话统计信息"""
        try:
            # 基础统计
            stats_sql = """
            SELECT 
                COUNT(*) as total_sessions,
                COUNT(DISTINCT language) as unique_languages,
                COUNT(DISTINCT framework) as unique_frameworks,
                MAX(created_at) as latest_session
            FROM qa_sessions
            """
            
            stats_result = await database_manager.execute_query(stats_sql, use_remote=False)
            stats_row = stats_result[0] if stats_result else {}

            # 语言分布
            language_sql = """
            SELECT language, COUNT(*) as count 
            FROM qa_sessions 
            WHERE language IS NOT NULL 
            GROUP BY language 
            ORDER BY count DESC
            """
            language_result = await database_manager.execute_query(language_sql, use_remote=False)
            language_distribution = {row['language']: row['count'] for row in language_result}

            # 来源分布
            source_sql = """
            SELECT source, COUNT(*) as count 
            FROM qa_sessions 
            GROUP BY source
            """
            source_result = await database_manager.execute_query(source_sql, use_remote=False)
            source_distribution = {row['source']: row['count'] for row in source_result}

            return {
                "total_sessions": stats_row.get('total_sessions', 0),
                "unique_languages": stats_row.get('unique_languages', 0),
                "unique_frameworks": stats_row.get('unique_frameworks', 0),
                "latest_session": stats_row.get('latest_session'),
                "language_distribution": language_distribution,
                "source_distribution": source_distribution
            }

        except Exception as e:
            logger.error(f"获取会话统计失败: {e}")
            return {}

    async def cleanup_old_sessions(self, days_old: int = 30) -> int:
        """清理旧的会话记录"""
        try:
            sql = """
            DELETE FROM qa_sessions 
            WHERE created_at < datetime('now', '-{} days')
            """.format(days_old)
            
            deleted_count = await database_manager.execute_update(sql, use_remote=False)
            logger.info(f"清理了 {deleted_count} 条旧会话记录（{days_old}天前）")
            return deleted_count

        except Exception as e:
            logger.error(f"清理旧会话记录失败: {e}")
            return 0


# 全局问答会话服务实例
qa_session_service = QASessionService()
