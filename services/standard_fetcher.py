"""
编码规范获取服务

实现从外部API获取编码规范并缓存到本地的功能
"""

import asyncio
import hashlib
import json
import logging
from datetime import datetime
from typing import Any, Dict, Optional

import httpx

from config.settings import config
from models.standard import StandardDocument, StandardMetadata, StandardSelector
from storage.file_manager import file_manager

logger = logging.getLogger("CodeStandardMCP.StandardFetcher")


class StandardFetcher:
    """编码规范获取服务类"""

    def __init__(self):
        # 配置超时设置
        self.connect_timeout = config.connect_timeout
        self.read_timeout = config.read_timeout
        self.write_timeout = config.write_timeout
        self.pool_timeout = config.pool_timeout
        self.http_timeout = config.http_timeout  # 保持向后兼容
        self.user_agent = config.user_agent
        self.max_retries = config.max_retries

    async def fetch_standard(
        self,
        selector: StandardSelector,
        force_refresh: bool = False,
    ) -> Dict[str, Any]:
        """
        获取编码规范

        Args:
            selector: 规范选择器
            force_refresh: 是否强制刷新

        Returns:
            包含规范内容和元数据的字典
        """
        try:
            import time
            fetch_start_time = time.time()
            cache_key = selector.to_cache_key()
            logger.info(f"📋 [FETCH] 开始获取规范 {cache_key} 于 {time.strftime('%H:%M:%S')}")
            logger.info(f"📋 [FETCH] force_refresh={force_refresh}")

            # 检查缓存（仅在非强制刷新时）
            if not force_refresh:
                cache_start_time = time.time()
                logger.info(f"🔍 [CACHE] 检查缓存 {cache_key}")
                cached_document = await self._get_cached_standard(selector)
                cache_duration = time.time() - cache_start_time

                if cached_document:
                    total_duration = time.time() - fetch_start_time
                    logger.info(f"💾 [CACHE] 缓存命中 {cache_key} (缓存检查: {cache_duration:.3f}s, 总计: {total_duration:.3f}s)")
                    return {
                        "success": True,
                        "source": "cache",
                        "content": cached_document.content,
                        "metadata": cached_document.to_dict()["metadata"],
                        "cached": True,
                    }
                else:
                    logger.info(f"❌ [CACHE] 缓存未命中 {cache_key} (缓存检查: {cache_duration:.3f}s)")

            # 从远程获取（慢路径）
            remote_start_time = time.time()
            logger.info(f"🌐 [REMOTE] 开始远程获取 {cache_key} 于 {time.strftime('%H:%M:%S')}")
            content = await self._fetch_from_remote(selector)
            remote_duration = time.time() - remote_start_time

            if not content:
                logger.warning(f"❌ [REMOTE] 远程获取失败 {cache_key} (耗时: {remote_duration:.3f}s)")

                # 构造友好的错误信息
                language_name = selector.language or "未知语言"
                framework_name = selector.framework or "通用"
                question_desc = selector.question or "编码规范"

                error_message = f"抱歉，暂时无法获取 {language_name} 编程语言"
                if selector.framework:
                    error_message += f" {framework_name} 框架"
                error_message += f"的{question_desc}信息。这可能是因为：\n"
                error_message += "1. 知识库中暂无相关内容\n"
                error_message += "2. 查询内容超出了当前支持范围\n\n"

                return {
                    "success": False,
                    "error": error_message,
                    "error_type": "content_unavailable",
                }

            logger.info(f"✅ [REMOTE] 远程获取成功 {cache_key} (耗时: {remote_duration:.3f}s)")

            # 创建规范文档
            doc_start_time = time.time()
            logger.info(f"📝 [DOC] 创建文档 {cache_key}")
            document = await self._create_standard_document(selector, content)
            doc_duration = time.time() - doc_start_time
            logger.info(f"✅ [DOC] 文档创建完成 (耗时: {doc_duration:.3f}s)")

            # 异步保存到缓存（不等待完成）
            logger.info(f"💾 [CACHE] 开始异步缓存保存 {cache_key}")
            asyncio.create_task(self._save_to_cache(document))

            total_duration = time.time() - fetch_start_time
            logger.info(f"✅ [FETCH] fetch_standard 完成 {cache_key} (总耗时: {total_duration:.3f}s)")

            return {
                "success": True,
                "source": "remote",
                "content": document.content,
                "metadata": document.to_dict()["metadata"],
                "cached": False,
            }

        except Exception as e:
            logger.error(f"❌ 获取规范时出错: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "unexpected_error",
            }

    async def _get_cached_standard(self, selector: StandardSelector) -> Optional[StandardDocument]:
        """获取缓存的规范文档"""
        try:
            cache_key = selector.to_cache_key()
            cache_file_path = config.get_cache_file_path(cache_key)

            if not cache_file_path.exists():
                logger.debug(f"📂 未找到缓存文件 {cache_key}")
                return None

            logger.info(f"📖 读取缓存文件 {cache_key}")
            data = await file_manager.read_json(cache_file_path)
            if not data:
                logger.warning(f"⚠️ 缓存数据为空 {cache_key}")
                return None

            logger.info(f"✅ 成功加载缓存规范 {cache_key}")
            return StandardDocument.model_validate(data)
        except Exception as e:
            logger.error(f"❌ 读取缓存规范时出错: {e}")
            return None

    async def _fetch_from_remote(self, selector: StandardSelector) -> Optional[str]:
        """从远程API获取内容"""
        try:
            # 从新的API接口获取编码规范
            content = await self._fetch_from_api(selector)
            if content:
                logger.info(f"✅ 成功从API获取 {selector.language}")
                return content

            logger.error(f"❌ 从API获取失败 {selector.language}")
            return None



        except Exception as e:
            logger.error(f"❌ 远程获取时出错: {e}")
            return None

    async def _fetch_from_api(self, selector: StandardSelector) -> Optional[str]:
        """从新的API接口获取编码规范"""
        try:
            import time
            api_start_time = time.time()

            # 构造问题字符串
            question = self._build_question(selector)
            logger.info(f"🔍 [API] 构造问题: {question}")

            # 准备请求数据
            request_data = {
                "history": [],
                "kb_ids": [],
                "question": question,
                "streaming": True,
                "user_id": config.get_api_user_id()
            }

            # 记录API调用详细信息
            if config.should_log_api_details():
                self._log_api_request(request_data)
            logger.info(f"📤 [API] 请求数据准备完成, user_id={config.get_api_user_id()}, streaming={request_data.get('streaming', False)}")

            # 实现重试机制
            logger.info(f"🌐 [API] 开始API获取，最大重试次数 {self.max_retries}")
            logger.info(f"🌐 [API] 超时配置: connect={self.connect_timeout}s, read={self.read_timeout}s, write={self.write_timeout}s, pool={self.pool_timeout}s")

            for attempt in range(self.max_retries):
                attempt_start_time = time.time()
                try:
                    logger.info(f"🔄 [API] 尝试 {attempt + 1}/{self.max_retries} 开始于 {time.strftime('%H:%M:%S')}")

                    # 发送HTTP请求 - 使用细粒度超时控制
                    timeout_config = httpx.Timeout(
                        connect=self.connect_timeout,
                        read=self.read_timeout,
                        write=self.write_timeout,
                        pool=self.pool_timeout
                    )

                    async with httpx.AsyncClient(timeout=timeout_config) as client:
                        headers = {
                            "User-Agent": self.user_agent,
                            "Content-Type": "application/json"
                        }

                        # 记录请求开始
                        request_start_time = time.time()
                        logger.info(f"📡 [HTTP] 发送POST请求到 {config.get_api_url()}")

                        response = await client.post(
                            config.get_api_url(),
                            json=request_data,
                            headers=headers
                        )

                        request_duration = time.time() - request_start_time
                        logger.info(f"📡 [HTTP] 收到响应: {response.status_code} (请求耗时: {request_duration:.3f}s)")

                        # 记录详细的API响应信息
                        if config.should_log_api_details():
                            self._log_api_response(response, request_duration)

                        response.raise_for_status()

                        # 处理响应（支持流式和非流式）
                        response_start_time = time.time()
                        logger.info(f"📄 [RESPONSE] 处理响应于 {time.strftime('%H:%M:%S')}")

                        # 检查响应类型并相应处理
                        response_data = None
                        try:
                            response_text = response.text

                            # 检测是否为流式响应（包含多行data:格式）
                            if 'data: {' in response_text and response_text.count('\n') > 1:
                                logger.info(f"🔄 [RESPONSE] 检测到流式响应")
                                response_data = self._parse_streaming_response(response_text)
                            else:
                                logger.info(f"📄 [RESPONSE] 处理标准JSON响应")
                                response_data = response.json()

                            if not isinstance(response_data, dict):
                                logger.error(f"❌ [API] 无效响应格式: 期望dict，得到 {type(response_data)}")
                                continue

                        except json.JSONDecodeError as e:
                            logger.error(f"❌ [API] 解析响应失败: {e}")
                            continue
                        except Exception as e:
                            logger.error(f"❌ [API] 处理响应时出错: {e}")
                            continue

                        # 使用更健壮的内容提取
                        content = self._extract_content_from_response(response_data)

                        response_duration = time.time() - response_start_time
                        attempt_duration = time.time() - attempt_start_time

                        if content:
                            # 验证内容相关性
                            if self._validate_content_relevance(content, selector):
                                logger.info(f"✅ [API] 第{attempt + 1}次尝试API获取成功")
                                logger.info(f"✅ [API] 响应处理: {response_duration:.3f}s, 尝试总计: {attempt_duration:.3f}s")
                                logger.info(f"✅ [API] 内容长度: {len(content)} 字符")

                                # 记录响应内容摘要
                                if config.should_log_api_details():
                                    self._log_response_content_summary(content)

                                return content
                            else:
                                logger.warning(f"⚠️ [API] 第{attempt + 1}次尝试API返回不相关内容，停止重试")
                                # 检测到不相关内容时直接返回None，不再重试
                                return None
                        else:
                            logger.warning(f"⚠️ [API] 第{attempt + 1}次尝试API返回空内容 (响应: {response_duration:.3f}s, 尝试: {attempt_duration:.3f}s)")

                except (httpx.TimeoutException, httpx.ConnectError) as e:
                    logger.warning(f"⚠️ 第{attempt + 1}次尝试网络错误: {e}")
                    if attempt == self.max_retries - 1:
                        logger.error("❌ 所有重试尝试均失败")
                        break
                    # 立即重试，不等待
                except httpx.HTTPStatusError as e:
                    logger.error(f"❌ HTTP错误 {e.response.status_code}: {e}")
                    break  # HTTP错误不重试
                except Exception as e:
                    logger.error(f"❌ 第{attempt + 1}次尝试意外错误: {e}")
                    if attempt == self.max_retries - 1:
                        break

            logger.warning("⚠️ API获取失败，返回None")
            return None

        except Exception as e:
            logger.error(f"❌ API获取时出错: {e}")
            return None

    def _build_question(self, selector: StandardSelector) -> str:
        """根据选择器参数构造问题字符串"""
        # 如果提供了具体问题，直接使用
        if selector.question:
            # 基础问题前缀
            question_parts = [f"请提供{selector.language}编程语言"]

            # 添加框架信息
            if selector.framework:
                question_parts.append(f"{selector.framework}框架")

            # 添加版本信息
            if selector.version:
                question_parts.append(f"{selector.version}版本")

            # 添加具体问题
            question_parts.append(f"关于{selector.question}的信息，控制在{config.get_response_limit()}字以内")

            return "".join(question_parts)

        # 如果没有提供具体问题，使用默认的编码规范问题
        question_parts = [f"请提供{selector.language}编程语言"]

        # 添加框架信息
        if selector.framework:
            question_parts.append(f"{selector.framework}框架")

        # 添加版本信息
        if selector.version:
            question_parts.append(f"{selector.version}版本")

        # 默认问题后缀
        question_parts.append(f"的编码规范，控制在{config.get_response_limit()}字以内")

        return "".join(question_parts)

    def _log_api_request(self, request_data: dict) -> None:
        """记录API请求详细信息"""
        try:
            if not config.should_log_api_details():
                return

            question = request_data.get("question", "")
            user_id = request_data.get("user_id", "")

            # 掩码敏感信息
            masked_user_id = config.mask_user_id(user_id) if user_id else "unknown"
            question_preview = config.truncate_text(question, 50)

            logger.info(
                f"🌐 [API_CALL] URL: {config.get_api_url()} | "
                f"Method: POST | Question: '{question_preview}' | "
                f"User: {masked_user_id} | Streaming: {request_data.get('streaming', False)}"
            )
        except Exception as e:
            logger.debug(f"记录API请求失败: {e}")

    def _log_api_response(self, response: httpx.Response, duration: float) -> None:
        """记录API响应详细信息"""
        try:
            if not config.should_log_api_details():
                return

            content_length = response.headers.get("content-length", "unknown")
            content_type = response.headers.get("content-type", "unknown")

            logger.info(
                f"📥 [API_RESPONSE] Status: {response.status_code} | "
                f"Content-Length: {content_length} | Content-Type: {content_type} | "
                f"Duration: {duration:.3f}s"
            )
        except Exception as e:
            logger.debug(f"记录API响应失败: {e}")

    def _log_response_content_summary(self, content: str) -> None:
        """记录响应内容摘要"""
        try:
            if not config.should_log_api_details():
                return

            content_size = len(content)
            content_preview = config.truncate_text(content.strip(), 100)

            logger.info(
                f"📄 [CONTENT_SUMMARY] Size: {content_size} chars | "
                f"Preview: '{content_preview}'"
            )
        except Exception as e:
            logger.debug(f"记录内容摘要失败: {e}")

    def _parse_streaming_response(self, response_text: str) -> Optional[dict]:
        """解析流式响应文本，提取最终的JSON数据"""
        try:
            lines = response_text.strip().split('\n')
            accumulated_response = ""
            final_data = None

            logger.info(f"🔄 [STREAMING] 解析流式响应，共 {len(lines)} 行")

            for line in lines:
                line = line.strip()
                if not line:
                    continue

                # 处理 "data: " 前缀
                if line.startswith('data: '):
                    json_str = line[6:]  # 移除 "data: " 前缀
                else:
                    json_str = line

                try:
                    data = json.loads(json_str)
                    if isinstance(data, dict):
                        # 累积response内容
                        response_part = data.get('response', '')
                        if response_part:
                            accumulated_response += response_part

                        # 保存最后一个有效的数据对象
                        final_data = data

                except json.JSONDecodeError:
                    continue

            if final_data:
                # 如果最终数据中有完整的history，优先使用
                history = final_data.get('history', [])
                if history and len(history) > 0:
                    logger.info("✅ [STREAMING] 从history中提取完整答案")
                    return final_data

                # 否则使用累积的response内容
                if accumulated_response.strip():
                    logger.info(f"✅ [STREAMING] 使用累积的response内容，长度: {len(accumulated_response)}")
                    # 创建一个包含累积内容的响应对象
                    return {
                        'code': final_data.get('code', 0),
                        'msg': final_data.get('msg', 'success'),
                        'response': accumulated_response,
                        'history': [[final_data.get('question', ''), accumulated_response]],
                        'source_documents': final_data.get('source_documents', [])
                    }

            logger.warning("⚠️ [STREAMING] 无法从流式响应中提取有效内容")
            return None

        except Exception as e:
            logger.error(f"❌ [STREAMING] 解析流式响应时出错: {e}")
            return None





    def _extract_content_from_response(self, response_data: dict) -> Optional[str]:
        """从API响应中提取编码规范内容（支持流式和非流式响应）"""
        try:
            # 根据实际API响应格式提取内容
            if isinstance(response_data, dict):
                # 检查API调用是否成功
                if response_data.get('code') != 0:
                    logger.error(f"❌ API返回错误代码: {response_data.get('code')}")
                    return None

                extracted_content = None
                extraction_source = None

                # 优先从history字段提取对话内容
                history = response_data.get('history', [])
                if history and len(history) > 0:
                    # history格式: [['question', 'answer'], ...]
                    last_conversation = history[-1]
                    if len(last_conversation) >= 2:
                        answer = last_conversation[1]
                        if isinstance(answer, str) and answer.strip():
                            extracted_content = answer.strip()
                            extraction_source = "history"
                            logger.info("✅ 成功从history提取内容")

                # 如果history中没有内容，尝试从response字段提取（支持流式响应的累积内容）
                if not extracted_content:
                    response_content = response_data.get('response', '')
                    if response_content and response_content.strip():
                        # 过滤掉流式响应的控制字符
                        cleaned_content = response_content.replace('data: [DONE]', '').strip()
                        if cleaned_content:
                            extracted_content = cleaned_content
                            extraction_source = "response"
                            logger.info("✅ 成功从response字段提取内容")

                # 记录最终提取的字符串详细信息
                if extracted_content:
                    self._log_extracted_content(extracted_content, extraction_source)
                    return extracted_content

                logger.warning("⚠️ 在API响应中找不到有效内容")
                return None

            logger.warning("⚠️ 响应不是字典格式")
            return None

        except Exception as e:
            logger.error(f"❌ 从响应中提取内容时出错: {e}")
            return None

    def _log_extracted_content(self, content: str, source: str) -> None:
        """记录提取的最终内容详细信息"""
        try:
            import time

            content_length = len(content)
            content_preview = config.truncate_text(content.strip(), 150) if hasattr(config, 'truncate_text') else content[:150] + "..." if len(content) > 150 else content

            # 计算行数
            line_count = content.count('\n') + 1

            # 记录详细的提取信息
            logger.info(f"📄 [EXTRACTED_CONTENT] 最终提取内容详情:")
            logger.info(f"📄 [EXTRACTED_CONTENT] 来源: {source}")
            logger.info(f"📄 [EXTRACTED_CONTENT] 长度: {content_length} 字符")
            logger.info(f"📄 [EXTRACTED_CONTENT] 行数: {line_count} 行")
            logger.info(f"📄 [EXTRACTED_CONTENT] 时间: {time.strftime('%Y-%m-%d %H:%M:%S')}")
            logger.info(f"📄 [EXTRACTED_CONTENT] 预览: '{content_preview}'")

            # 如果启用了详细日志，记录完整内容
            if config.should_log_api_details():
                logger.debug(f"📄 [EXTRACTED_CONTENT] 完整内容:\n{content}")

        except Exception as e:
            logger.debug(f"记录提取内容详情失败: {e}")

    def _validate_content_relevance(self, content: str, selector: StandardSelector) -> bool:
        """验证内容是否与查询相关"""
        if not content or not selector:
            return False

        content_lower = content.lower()

        # 检查是否包含明显的错误响应标识
        error_indicators = [
            "short out case",
            "short circuit",
            "electrical system",
            "circuit breakers",
            "insulation failure",
            "overloading",
            "physical damage",
            "没有找到相关的素材",
            "没有找到相关素材",
            "找不到相关的素材",
            "找不到相关素材",
            "无法找到相关的素材",
            "无法找到相关素材",
            "知识库中没有找到",
            "知识库中找不到",
            "无法回答您的问题"
        ]

        # 如果包含错误响应标识，直接判定为不相关
        for indicator in error_indicators:
            if indicator in content_lower:
                logger.warning(f"⚠️ [VALIDATION] 检测到不相关内容，包含错误指示词: '{indicator}'")
                return False

        # 移除编程关键词检测，直接通过验证
        logger.info(f"✅ [VALIDATION] 内容验证通过")
        return True

    def _get_hardcoded_standard(self, selector: StandardSelector) -> Optional[str]:
        """获取硬编码的编码规范（已移除硬编码逻辑）"""
        # 硬编码逻辑已移除，返回None以使用API获取
        return None



    def _create_standard_document_sync(self, selector: StandardSelector, content: str) -> StandardDocument:
        """创建规范文档（同步版本，用于快速路径）"""
        # 计算内容哈希
        content_hash = hashlib.md5(content.encode()).hexdigest()

        # 创建元数据
        metadata = StandardMetadata(
            selector=selector,
            fetch_date=datetime.now(),
            source_url=None,  # 硬编码规范没有URL
            content_hash=content_hash,
        )

        return StandardDocument(metadata=metadata, content=content)

    async def _create_standard_document(self, selector: StandardSelector, content: str) -> StandardDocument:
        """创建规范文档（异步版本）"""
        # 计算内容哈希
        content_hash = hashlib.md5(content.encode()).hexdigest()

        # 创建元数据
        metadata = StandardMetadata(
            selector=selector,
            fetch_date=datetime.now(),
            source_url=config.get_api_url(),
            content_hash=content_hash,
        )

        return StandardDocument(metadata=metadata, content=content)

    async def _save_to_cache(self, document: StandardDocument) -> bool:
        """保存到缓存"""
        try:
            cache_key = document.metadata.selector.to_cache_key()
            cache_file_path = config.get_cache_file_path(cache_key)

            logger.info(f"💾 保存规范到缓存: {cache_key}")

            # 确保缓存目录存在
            cache_file_path.parent.mkdir(parents=True, exist_ok=True)

            # 保存文档
            data = document.model_dump()
            success = await file_manager.write_json(cache_file_path, data)

            if success:
                logger.info(f"✅ 规范缓存成功: {cache_key}")
            else:
                logger.warning(f"⚠️ 规范缓存失败: {cache_key}")

            return success
        except Exception as e:
            logger.error(f"❌ 保存到缓存时出错: {e}")
            return False

    def _get_general_standard(self) -> str:
        """通用编码规范"""
        return """# EEM Base Fusion Config SDK

EEM Base Fusion Config SDK 是一个基于 Spring Boot 的能源管理配置软件开发工具包，提供了完整的能源管理系统配置功能，包括节点管理、产品管理、能源类型管理、设备管理等核心功能。

## 快速开始

### 1. 添加依赖

在您的 Spring Boot 项目中添加 SDK 依赖：

```xml
<eem-base-fusion-config-sdk.version>5.0.13.Alpha</eem-base-fusion-config-sdk.version>

<dependency>
    <groupId>com.cet.electric</groupId>
    <artifactId>eem-base-fusion-config-sdk</artifactId>
    <version>${eem-base-fusion-config-sdk.version}</version>
</dependency>
```

## 核心功能模块

### 1. 节点管理 (EemNodeService)

提供节点树的查询、管理和权限控制功能：

- 节点树查询（支持权限过滤）
- 节点详情查询
- 子节点查询
- 根节点查询
- 设备分类查询

### 2. 产品管理 (ProductService)

完整的产品生命周期管理：

- 产品创建、编辑、删除
- 产品查询和列表
- 产品数据录入
- 产品类型管理

### 3. 能源类型管理 (ProjectEnergyTypeService)

能源类型的配置和查询：

- 租户能源类型查询
- 折标能源类型管理
- 扩展能源类型支持

### 4. 设备核心服务 (PecCoreService)

设备相关的核心功能：

- 设备节点信息查询
- 测量点管理
- 设备树查询
- 报表树分级查询

### 5. 单位管理 (EnergyUnitService)

能源单位的定义和管理：

- 默认单位查询
- 用户自定义单位
- 单位转换系数

### 6. 拓扑管理 (TopologyService)

节点关系和拓扑结构管理：

- 节点关系查询
- 拓扑树获取
- 拓扑树裁剪

## API接口文档

### 认证和权限

所有API接口都需要在请求头中包含以下认证信息：

| 请求头 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `X-Auth-Tenant` | Long | 是 | 租户ID，用于多租户隔离 |
| `User-ID` | Long | 部分接口 | 用户ID，用于权限控制 |
| `Content-Type` | String | POST/PUT | application/json |

### 统一响应格式

所有接口都返回统一的响应格式：

```json
{
    "code": 200,
    "message": "success",
    "data": {
        // 具体数据内容
    },
    "success": true
}
```

### 错误响应格式

```json
{
    "code": 400,
    "message": "参数错误",
    "data": null,
    "success": false
}
```

---

## 1. 节点管理API

### 1.1 查询节点树

查询指定租户或节点下的节点树结构，支持权限过滤。

**接口地址：** `POST /tree`

**请求头：**
```http
Content-Type: application/json
X-Auth-Tenant: {tenantId}
User-ID: {userId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| parentNode | Object | 否 | 父节点信息，不传则查询租户根节点 |
| parentNode.id | Long | 是 | 节点ID |
| parentNode.modelLabel | String | 是 | 节点类型标签 |
| maxDepth | Integer | 否 | 最大查询深度，默认无限制 |
| nodeTreeGroupId | Integer | 否 | 节点树分组ID |
| energyType | Integer | 否 | 能源类型过滤 |

**请求示例：**
```json
{
    "parentNode": {
        "id": 1,
        "modelLabel": "tenant"
    },
    "maxDepth": 3,
    "energyType": 5001
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "modelLabel": "building",
            "name": "办公楼A",
            "energytype": 5001,
            "associativePecDevice": true,
            "isHierarchy": false,
            "roomtype": 1,
            "tree_id": "1_building",
            "childSelectState": 1,
            "children": [
                {
                    "id": 2,
                    "modelLabel": "floor",
                    "name": "1楼",
                    "energytype": 5001,
                    "children": []
                }
            ]
        }
    ],
    "success": true
}
```

**SDK调用示例：**
```java
@Autowired
private EemNodeService eemNodeService;

public List<NodeTreeDTO> getNodeTree(Long tenantId, Long parentNodeId) {
    NodeTreeQueryDTO queryDTO = new NodeTreeQueryDTO();
    queryDTO.setTenantId(tenantId);

    if (parentNodeId != null) {
        BaseEntity parentNode = new BaseEntity();
        parentNode.setId(parentNodeId);
        parentNode.setModelLabel("building");
        queryDTO.setParentNode(parentNode);
    }

    return eemNodeService.queryNodeTree(queryDTO);
}
```

### 1.2 查询节点详情

批量查询节点的详细信息。

**接口地址：** `POST /detail`

**请求头：**
```http
Content-Type: application/json
```

**请求参数：**

请求体为节点数组，每个节点包含：

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 节点ID |
| modelLabel | String | 是 | 节点类型标签 |

**请求示例：**
```json
[
    {
        "id": 1,
        "modelLabel": "building"
    },
    {
        "id": 2,
        "modelLabel": "floor"
    }
]
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "modelLabel": "building",
            "name": "办公楼A",
            "description": "主办公楼",
            "createTime": "2024-01-01 10:00:00",
            "updateTime": "2024-01-01 10:00:00"
        }
    ],
    "success": true
}
```

### 1.3 查询子层级信息

查询指定父节点下的子层级节点信息。

**接口地址：** `POST /child-label/query`

**请求头：**
```http
Content-Type: application/json
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| parentNode | Object | 是 | 父节点信息 |
| parentNode.id | Long | 是 | 父节点ID |
| parentNode.modelLabel | String | 是 | 父节点类型标签 |
| subLabelList | Array | 否 | 子节点类型过滤列表 |
| energyTypes | Array | 否 | 能源类型过滤列表 |

**请求示例：**
```json
{
    "parentNode": {
        "id": 1,
        "modelLabel": "building"
    },
    "subLabelList": ["floor", "room"],
    "energyTypes": [5001, 5002]
}
```

### 1.4 查询设备分类

查询设备分类数据，支持名称模糊搜索。

**接口地址：** `GET /deviceClassification`

**请求头：**
```http
X-Auth-Tenant: {tenantId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | String | 否 | 设备分类名称，支持模糊搜索 |

**请求示例：**
```http
GET /deviceClassification?name=空调
X-Auth-Tenant: 1001
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "name": "中央空调",
            "code": "HVAC_001",
            "description": "中央空调设备分类"
        }
    ],
    "success": true
}
```

### 1.5 查询根节点

查询当前租户的根节点信息，支持用户权限过滤。

**接口地址：** `GET /root-node`

**请求头：**
```http
X-Auth-Tenant: {tenantId}
User-ID: {userId}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "modelLabel": "tenant",
            "name": "租户根节点",
            "energytype": null,
            "associativePecDevice": false,
            "isHierarchy": true,
            "children": []
        }
    ],
    "success": true
}
```

---

## 2. 产品管理API

### 2.1 创建产品

创建新的产品类型。

**接口地址：** `POST /products`

**请求头：**
```http
Content-Type: application/json
X-Auth-Tenant: {tenantId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| name | String | 是 | 产品名称 |
| producttype | Integer | 是 | 产品类型ID |
| unitprice | Double | 否 | 单价 |

**请求示例：**
```json
{
    "name": "电力产品A",
    "producttype": 5001,
    "unitprice": 100.50
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "创建成功",
    "data": null,
    "success": true
}
```

**SDK调用示例：**
```java
@Autowired
private ProductService productService;

public void createProduct(String name, Integer productType, Double unitPrice, Long tenantId) {
    CreateProductVO createProductVO = new CreateProductVO();
    createProductVO.setName(name);
    createProductVO.setProductType(productType);
    createProductVO.setUnitPrice(unitPrice);
    createProductVO.setTenantId(tenantId);

    productService.createProduct(createProductVO);
}
```

### 2.2 编辑产品

编辑现有产品信息。

**接口地址：** `PUT /products`

**请求头：**
```http
Content-Type: application/json
X-Auth-Tenant: {tenantId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| id | Long | 是 | 产品ID |
| name | String | 是 | 产品名称 |
| producttype | Integer | 是 | 产品类型ID |
| unitprice | Double | 否 | 单价 |

**请求示例：**
```json
{
    "id": 1,
    "name": "电力产品A（修改版）",
    "producttype": 5001,
    "unitprice": 120.00
}
```

### 2.3 删除产品

批量删除产品。

**接口地址：** `DELETE /products`

**请求头：**
```http
Content-Type: application/json
X-Auth-Tenant: {tenantId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| productIds | Array | 是 | 要删除的产品ID列表 |

**请求示例：**
```json
{
    "productIds": [1, 2, 3]
}
```

### 2.4 查询产品列表

查询当前租户下的所有产品。

**接口地址：** `GET /products`

**请求头：**
```http
X-Auth-Tenant: {tenantId}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "name": "电力产品A",
            "producttype": 5001,
            "unitprice": 100.50,
            "createTime": "2024-01-01 10:00:00"
        }
    ],
    "success": true
}
```

---

## 3. 能源类型管理API

### 3.1 查询租户能源类型

查询租户下的所有能源类型，可选择是否包含扩展能源类型。

**接口地址：** `GET /projectEnergy`

**请求头：**
```http
X-Auth-Tenant: {tenantId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| queryExtendEnergyType | Boolean | 否 | 是否查询扩展能源类型，默认false |

**请求示例：**
```http
GET /projectEnergy?queryExtendEnergyType=true
X-Auth-Tenant: 1001
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 5001,
            "name": "电力",
            "symbol": "kWh",
            "symbolCn": "千瓦时",
            "unitprice": 0.8,
            "unitsymbol": 1001,
            "unitCoef": 1.0,
            "unitmultiplier": 1,
            "isStandardized": false
        }
    ],
    "success": true
}
```

### 3.2 查询非折标能源类型

查询租户下的非折标能源类型。

**接口地址：** `GET /projectEnergy/no-standardized`

**请求头：**
```http
X-Auth-Tenant: {tenantId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| queryExtendEnergyType | Boolean | 否 | 是否查询扩展能源类型 |

### 3.3 查询折标能源类型

查询租户下的折标能源类型。

**接口地址：** `GET /projectEnergy/standardized`

**请求头：**
```http
X-Auth-Tenant: {tenantId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| queryExtendEnergyType | Boolean | 否 | 是否查询扩展能源类型 |

### 3.4 查询排序能源类型

查询租户能源类型，折标能源类型排在前面。

**接口地址：** `GET /projectEnergy/order`

**请求头：**
```http
X-Auth-Tenant: {tenantId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| queryExtendEnergyType | Boolean | 否 | 是否查询扩展能源类型 |

---

## 4. 单位管理API

### 4.1 查询默认单位

根据能源或产品类型查询默认单位信息。

**接口地址：** `POST /default-unit`

**请求头：**
```http
Content-Type: application/json
X-Auth-Tenant: {tenantId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| projectUnitClassify | Integer | 是 | 单位类型归类：1能耗，2产品，3定时记录 |

**请求体：** 能源或产品类型ID数组

**请求示例：**
```http
POST /default-unit?projectUnitClassify=1
Content-Type: application/json
X-Auth-Tenant: 1001

[5001, 5002, 5003]
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "type": 5001,
            "typeName": "电力",
            "unitEn": "kWh",
            "unitCn": "千瓦时",
            "coef": 1.0,
            "basicUnitSymbol": 1001,
            "basicUnitSymbolName": "千瓦时",
            "projectUnitClassify": 1
        }
    ],
    "success": true
}
```

### 4.2 查询用户自定义单位

查询用户自定义的单位信息。

**接口地址：** `POST /define-unit`

**请求头：**
```http
Content-Type: application/json
X-Auth-Tenant: {tenantId}
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| type | Integer | 否 | 单位类型ID |
| projectUnitClassify | Integer | 否 | 单位类型归类 |
| value | Double | 否 | 查询值 |

**请求示例：**
```json
{
    "type": 5001,
    "projectUnitClassify": 1,
    "value": 100.0
}
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 2,
            "type": 5001,
            "typeName": "电力",
            "unitEn": "MWh",
            "unitCn": "兆瓦时",
            "coef": 1000.0,
            "projectUnitClassify": 1,
            "tenantId": 1001
        }
    ],
    "success": true
}
```

---

## 5. 树配置API

### 5.1 查询节点关系树

查询全量节点树结构配置。

**接口地址：** `GET /query`

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| nodeTreeGroupId | Integer | 否 | 节点树分组ID，为空则返回全量节点树 |

**请求示例：**
```http
GET /query?nodeTreeGroupId=1
```

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": [
        {
            "id": 1,
            "parentId": null,
            "modelLabel": "tenant",
            "name": "租户",
            "level": 0,
            "children": [
                {
                    "id": 2,
                    "parentId": 1,
                    "modelLabel": "building",
                    "name": "建筑",
                    "level": 1,
                    "children": []
                }
            ]
        }
    ],
    "success": true
}
```

### 5.2 查询指定节点类型

查询指定节点类型及其子级配置。

**接口地址：** `POST /findNode`

**请求头：**
```http
Content-Type: application/json
```

**请求参数：**

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| modelLabel | String | 是 | 节点类型标签 |
| includeChildren | Boolean | 否 | 是否包含子级，默认true |

**请求示例：**
```json
{
    "modelLabel": "building",
    "includeChildren": true
}
```

### 5.3 查询根节点标签

查询根节点的标签配置。

**接口地址：** `GET /root-label`

**响应示例：**
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1,
        "modelLabel": "tenant",
        "name": "租户",
        "level": 0
    },
    "success": true
}
```

---

## 配置参数

### 事件颜色配置

```properties
# 是否加载事件颜色配置
cet.eem.common.loadEventColorConfig=true

# 事件颜色配置文件路径
cet.eem.event-color.path=/path/to/event/colors
```

### 时间配置

```properties
# 时间配置
common.date.startMinute=0
common.date.startHour=0
common.date.startDay=1
common.date.startMonth=1
common.date.startWeek=1
common.date.belong=1
```

## 使用示例

### 1. 节点树查询示例

```java
@Service
public class NodeManagementService {

    @Autowired
    private EemNodeService eemNodeService;

    public List<NodeTreeDTO> getNodeTree(Long tenantId, Long parentNodeId) {
        NodeTreeQueryDTO queryDTO = new NodeTreeQueryDTO();
        queryDTO.setTenantId(tenantId);

        if (parentNodeId != null) {
            BaseEntity parentNode = new BaseEntity();
            parentNode.setId(parentNodeId);
            parentNode.setModelLabel("building");
            queryDTO.setParentNode(parentNode);
        }

        return eemNodeService.queryNodeTree(queryDTO);
    }
}
```

### 2. 产品管理示例

```java
@Service
public class ProductManagementService {

    @Autowired
    private ProductService productService;

    public void createProduct(String name, Integer productType, Double unitPrice, Long tenantId) {
        CreateProductVO createProductVO = new CreateProductVO();
        createProductVO.setName(name);
        createProductVO.setProductType(productType);
        createProductVO.setUnitPrice(unitPrice);
        createProductVO.setTenantId(tenantId);

        productService.createProduct(createProductVO);
    }

    public List<ProductVO> getProducts(Long tenantId) {
        return productService.queryProducts(tenantId);
    }

    public void editProduct(Long id, String name, Integer productType, Double unitPrice, Long tenantId) {
        EditProductVO editProductVO = new EditProductVO();
        editProductVO.setId(id);
        editProductVO.setName(name);
        editProductVO.setProductType(productType);
        editProductVO.setUnitPrice(unitPrice);
        editProductVO.setTenantId(tenantId);

        productService.editProduct(editProductVO);
    }
}
```

### 3. 能源类型查询示例

```java
@Service
public class EnergyTypeManagementService {

    @Autowired
    private ProjectEnergyTypeService projectEnergyTypeService;

    public List<EnergyTypeWithUnitDTO> getAllEnergyTypes(Long tenantId, Boolean includeExtended) {
        return projectEnergyTypeService.queryEnergyTypesWithFilterExtendType(
            tenantId, null, includeExtended);
    }

    public List<EnergyTypeWithUnitDTO> getStandardizedEnergyTypes(Long tenantId) {
        return projectEnergyTypeService.queryEnergyTypesStandardized(tenantId, false);
    }

    public List<EnergyTypeWithUnitDTO> getNonStandardizedEnergyTypes(Long tenantId) {
        return projectEnergyTypeService.queryEnergyTypesWithOutStandardized(tenantId, false);
    }
}
```

### 4. 单位管理示例

```java
@Service
public class UnitManagementService {

    @Autowired
    private EnergyUnitService energyUnitService;

    public List<UserDefineUnitDTO> getDefaultUnits(List<Integer> types, Integer classify, Long tenantId) {
        return energyUnitService.queryBasicUserDefineUnit(types, classify, tenantId);
    }

    public List<UserDefineUnitDTO> getUserDefinedUnits(Integer type, Integer classify, Long tenantId) {
        UserDefineUnitSearchDTO searchDTO = new UserDefineUnitSearchDTO();
        searchDTO.setType(type);
        searchDTO.setProjectUnitClassify(classify);
        searchDTO.setTenantId(tenantId);

        return energyUnitService.queryUserDefineUnit(searchDTO);
    }
}
```

### 5. 设备信息查询示例

```java
@Service
public class DeviceManagementService {

    @Autowired
    private PecCoreService pecCoreService;

    public List<DeviceNodeInfo> getDeviceInfo(List<Long> deviceIds) {
        return pecCoreService.queryDeviceInfos(deviceIds);
    }

    public Set<Integer> checkMeasurementPoints(List<Integer> nodeIds, Set<Integer> measurementPoints) {
        return pecCoreService.containsMeasurementPoint(nodeIds, measurementPoints);
    }

    public ApiResult<List<GraphNodeVo>> getReportTree(Integer parentId, Integer depth) {
        return pecCoreService.queryReportTreeGrading(parentId, depth);
    }
}
```

### 6. 完整的控制器示例

```java
@RestController
@RequestMapping("/api/demo")
public class DemoController {

    @Autowired
    private EemNodeService eemNodeService;

    @Autowired
    private ProductService productService;

    @Autowired
    private ProjectEnergyTypeService energyTypeService;

    @PostMapping("/node-tree")
    public ApiResult<List<NodeTreeDTO>> getNodeTree(
            @RequestHeader("X-Auth-Tenant") Long tenantId,
            @RequestHeader("User-ID") Long userId,
            @RequestBody NodeTreeQueryDTO queryDTO) {

        queryDTO.setTenantId(tenantId);
        queryDTO.setUserId(userId);

        List<NodeTreeDTO> result = eemNodeService.queryNodeTree(queryDTO);
        return Result.ok(result);
    }

    @GetMapping("/products")
    public ApiResult<List<ProductVO>> getProducts(
            @RequestHeader("X-Auth-Tenant") Long tenantId) {

        List<ProductVO> products = productService.queryProducts(tenantId);
        return Result.ok(products);
    }

    @GetMapping("/energy-types")
    public ApiResult<List<EnergyTypeWithUnitDTO>> getEnergyTypes(
            @RequestHeader("X-Auth-Tenant") Long tenantId,
            @RequestParam(required = false) Boolean queryExtendEnergyType) {

        List<EnergyTypeWithUnitDTO> energyTypes = energyTypeService
            .queryEnergyTypesWithFilterExtendType(tenantId, null, queryExtendEnergyType);
        return Result.ok(energyTypes);
    }
}
```

## 错误处理

### 详细错误码说明

| 错误码 | HTTP状态码 | 描述 | 常见原因 | 解决方案 |
|--------|------------|------|----------|----------|
| 200 | 200 | 请求成功 | - | - |
| 400 | 400 | 请求参数错误 | 参数格式错误、必填参数缺失 | 检查请求参数格式和必填字段 |
| 401 | 401 | 未授权访问 | 缺少认证信息 | 检查 X-Auth-Tenant 和 User-ID 请求头 |
| 403 | 403 | 权限不足 | 用户无权限访问资源 | 检查用户权限配置 |
| 404 | 404 | 资源不存在 | 请求的资源ID不存在 | 确认资源 ID 是否正确 |
| 409 | 409 | 资源冲突 | 数据重复、状态冲突 | 检查数据唯一性约束 |
| 422 | 422 | 数据验证失败 | 业务规则验证失败 | 检查业务逻辑和数据有效性 |
| 500 | 500 | 服务器内部错误 | 系统异常、数据库连接失败 | 查看服务器日志排查问题 |

### 常见错误示例

#### 1. 参数验证错误
```json
{
    "code": 400,
    "message": "产品名称不能为空",
    "data": null,
    "success": false
}
```

#### 2. 权限不足错误
```json
{
    "code": 403,
    "message": "用户无权限访问该节点",
    "data": null,
    "success": false
}
```

#### 3. 资源不存在错误
```json
{
    "code": 404,
    "message": "产品不存在",
    "data": null,
    "success": false
}
```

#### 4. 业务逻辑错误
```json
{
    "code": 422,
    "message": "产品类型配置已存在",
    "data": null,
    "success": false
}
```

"""


# 创建全局实例
standard_fetcher = StandardFetcher()
