"""
反馈服务

提供反馈数据的存储、同步和统计功能
"""

import asyncio
import logging
from datetime import datetime
from typing import Any, Dict, List, Optional

from config.settings import config
from models.feedback import FeedbackData, FeedbackSubmission, FeedbackStats, SyncResult
from storage.database_manager import database_manager

logger = logging.getLogger("CodeStandardMCP.FeedbackService")


class FeedbackService:
    """反馈服务类"""

    def __init__(self):
        self.sync_enabled = config.feedback_sync_enabled
        self.retry_count = config.feedback_sync_retry_count
        self.sync_timeout = config.feedback_sync_timeout

    async def initialize(self):
        """初始化反馈服务"""
        try:
            success = await database_manager.initialize_databases()
            if success:
                logger.info("反馈服务初始化成功")
            else:
                logger.warning("反馈服务初始化部分失败")
            return success
        except Exception as e:
            logger.error(f"反馈服务初始化失败: {e}")
            return False

    async def submit_feedback(self, submission: FeedbackSubmission) -> Dict[str, Any]:
        """提交反馈"""
        try:
            # 创建反馈数据
            feedback_data = FeedbackData(
                question=submission.question,
                answer=submission.answer,
                rating=submission.rating,
                comment=submission.comment,
                session_id=submission.session_id,
                user_id=submission.user_id,
                created_at=datetime.now(),
                sync_status="pending"
            )

            # 保存到本地数据库
            feedback_id = await self._save_to_local(feedback_data)
            feedback_data.id = feedback_id

            logger.info(f"反馈已保存到本地数据库，ID: {feedback_id}")

            # 尝试同步到远程数据库
            sync_success = False
            if self.sync_enabled:
                sync_result = await self._sync_single_feedback(feedback_data)
                sync_success = sync_result.success

            return {
                "success": True,
                "feedback_id": feedback_id,
                "synced_to_remote": sync_success,
                "message": "反馈提交成功" + ("并已同步到远程数据库" if sync_success else "，远程同步将稍后重试" if self.sync_enabled else "")
            }

        except Exception as e:
            logger.error(f"提交反馈失败: {e}")
            return {
                "success": False,
                "error": str(e),
                "error_type": "submission_failed"
            }

    async def _save_to_local(self, feedback_data: FeedbackData) -> int:
        """保存反馈到本地数据库"""
        sql = """
        INSERT INTO feedback (question, answer, rating, comment, session_id, user_id, created_at, sync_status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        """
        params = (
            feedback_data.question,
            feedback_data.answer,
            feedback_data.rating,
            feedback_data.comment,
            feedback_data.session_id,
            feedback_data.user_id,
            feedback_data.created_at.isoformat(),
            feedback_data.sync_status
        )
        
        return await database_manager.execute_insert(sql, params, use_remote=False)

    async def _sync_single_feedback(self, feedback_data: FeedbackData) -> SyncResult:
        """同步单个反馈到远程数据库"""
        sync_time = datetime.now()
        
        try:
            # 保存到远程数据库
            sql = """
            INSERT INTO feedback (question, answer, rating, comment, session_id, user_id, created_at)
            VALUES (?, ?, ?, ?, ?, ?, ?)
            """
            params = (
                feedback_data.question,
                feedback_data.answer,
                feedback_data.rating,
                feedback_data.comment,
                feedback_data.session_id,
                feedback_data.user_id,
                feedback_data.created_at.isoformat()
            )
            
            remote_id = await database_manager.execute_insert(sql, params, use_remote=True)
            
            # 更新本地同步状态
            await self._update_sync_status(feedback_data.id, "synced", sync_time)
            
            logger.info(f"反馈同步成功，本地ID: {feedback_data.id}, 远程ID: {remote_id}")
            
            return SyncResult(
                success=True,
                synced_count=1,
                failed_count=0,
                sync_time=sync_time
            )
            
        except Exception as e:
            logger.error(f"反馈同步失败: {e}")
            
            # 更新本地同步状态为失败
            await self._update_sync_status(feedback_data.id, "failed", sync_time)
            
            return SyncResult(
                success=False,
                synced_count=0,
                failed_count=1,
                error_message=str(e),
                sync_time=sync_time
            )

    async def _update_sync_status(self, feedback_id: int, status: str, sync_time: datetime):
        """更新本地反馈的同步状态"""
        sql = "UPDATE feedback SET sync_status = ?, synced_at = ? WHERE id = ?"
        params = (status, sync_time.isoformat(), feedback_id)
        await database_manager.execute_update(sql, params, use_remote=False)

    async def sync_pending_feedback(self) -> SyncResult:
        """同步所有待同步的反馈"""
        sync_time = datetime.now()
        synced_count = 0
        failed_count = 0
        errors = []

        try:
            # 获取所有待同步的反馈
            pending_feedback = await self._get_pending_feedback()
            
            if not pending_feedback:
                logger.info("没有待同步的反馈")
                return SyncResult(
                    success=True,
                    synced_count=0,
                    failed_count=0,
                    sync_time=sync_time
                )

            logger.info(f"开始同步 {len(pending_feedback)} 条待同步反馈")

            # 逐个同步
            for feedback in pending_feedback:
                try:
                    result = await self._sync_single_feedback(feedback)
                    if result.success:
                        synced_count += 1
                    else:
                        failed_count += 1
                        if result.error_message:
                            errors.append(f"ID {feedback.id}: {result.error_message}")
                            
                except Exception as e:
                    failed_count += 1
                    errors.append(f"ID {feedback.id}: {str(e)}")
                    logger.error(f"同步反馈 {feedback.id} 失败: {e}")

            success = failed_count == 0
            logger.info(f"批量同步完成，成功: {synced_count}, 失败: {failed_count}")

            return SyncResult(
                success=success,
                synced_count=synced_count,
                failed_count=failed_count,
                error_message="; ".join(errors) if errors else None,
                sync_time=sync_time
            )

        except Exception as e:
            logger.error(f"批量同步失败: {e}")
            return SyncResult(
                success=False,
                synced_count=synced_count,
                failed_count=failed_count,
                error_message=str(e),
                sync_time=sync_time
            )

    async def _get_pending_feedback(self) -> List[FeedbackData]:
        """获取所有待同步的反馈"""
        sql = "SELECT * FROM feedback WHERE sync_status = 'pending' ORDER BY created_at"
        rows = await database_manager.execute_query(sql, use_remote=False)
        
        feedback_list = []
        for row in rows:
            feedback = FeedbackData(
                id=row['id'],
                question=row['question'],
                answer=row['answer'],
                rating=row['rating'],
                comment=row['comment'],
                session_id=row['session_id'],
                user_id=row['user_id'],
                created_at=datetime.fromisoformat(row['created_at']),
                synced_at=datetime.fromisoformat(row['synced_at']) if row['synced_at'] else None,
                sync_status=row['sync_status']
            )
            feedback_list.append(feedback)
        
        return feedback_list

    async def get_feedback_stats(self) -> FeedbackStats:
        """获取反馈统计信息"""
        try:
            # 基础统计
            stats_sql = """
            SELECT 
                COUNT(*) as total_count,
                AVG(rating) as average_rating,
                COUNT(CASE WHEN sync_status = 'pending' THEN 1 END) as pending_sync_count,
                COUNT(CASE WHEN sync_status = 'failed' THEN 1 END) as failed_sync_count,
                MAX(synced_at) as last_sync_time
            FROM feedback
            """
            
            stats_result = await database_manager.execute_query(stats_sql, use_remote=False)
            stats_row = stats_result[0] if stats_result else {}

            # 评分分布
            rating_sql = "SELECT rating, COUNT(*) as count FROM feedback GROUP BY rating"
            rating_result = await database_manager.execute_query(rating_sql, use_remote=False)
            rating_distribution = {row['rating']: row['count'] for row in rating_result}

            return FeedbackStats(
                total_count=stats_row.get('total_count', 0),
                average_rating=round(stats_row.get('average_rating', 0.0), 2),
                rating_distribution=rating_distribution,
                pending_sync_count=stats_row.get('pending_sync_count', 0),
                failed_sync_count=stats_row.get('failed_sync_count', 0),
                last_sync_time=datetime.fromisoformat(stats_row['last_sync_time']) if stats_row.get('last_sync_time') else None
            )

        except Exception as e:
            logger.error(f"获取反馈统计失败: {e}")
            return FeedbackStats()

    async def test_database_connections(self) -> Dict[str, Any]:
        """测试数据库连接"""
        return await database_manager.test_connections()


# 全局反馈服务实例
feedback_service = FeedbackService()
